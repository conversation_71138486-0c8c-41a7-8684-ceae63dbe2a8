# Store Bot - ربات فروشگاه تلگرام

یک ربات تلگرام کامل برای مدیریت فروشگاه آنلاین با قابلیت‌های پیشرفته مدیریت محصولات، پرداخت و پشتیبانی.

## ویژگی‌های اصلی

### 🛍️ مدیریت فروشگاه
- **مدیریت محصولات**: افزودن، ویرایش و حذف محصولات
- **دسته‌بندی محصولات**: ساختار درختی دسته‌بندی‌ها
- **سفارش‌گیری**: ثبت و پیگیری سفارشات
- **سفارش خودکار**: امکان تنظیم سفارشات تکراری
- **موجودی انبار**: مدیریت موجودی محصولات

### 💳 سیستم پرداخت
- **درگاه‌های پرداخت**: پشتیبانی از زرین‌پال و زیبال
- **کیف پول**: سیستم اعتبار داخلی
- **انتقال اعتبار**: امکان انتقال اعتبار بین کاربران
- **تاریخچه پرداخت**: مشاهده تراکنش‌های انجام شده

### 👥 مدیریت کاربران
- **احراز هویت**: تایید شماره تلفن
- **پروفایل کاربری**: مدیریت اطلاعات شخصی
- **سطح دسترسی**: سیستم مدیریت ادمین‌ها
- **بلاک/آنبلاک**: مدیریت کاربران مسدود شده

### 🔧 پنل مدیریت
- **آمار فروش**: گزارش‌های تفصیلی فروش
- **مدیریت متون**: ویرایش پیام‌های ربات
- **تنظیمات کانال**: مدیریت کانال‌های گزارش
- **وب‌سرویس**: اتصال به API های خارجی

### 🤖 API و اتوماسیون
- **وب‌سرویس**: اتصال به سرویس‌های خارجی
- **بروزرسانی خودکار**: آپدیت محصولات از API
- **CRON Jobs**: وظایف زمان‌بندی شده
- **گزارش‌گیری**: ارسال خودکار گزارش‌ها

## نصب و راه‌اندازی

### پیش‌نیازها
- PHP 7.4 یا بالاتر
- MySQL/MariaDB
- سرور وب (Apache/Nginx)
- SSL Certificate (برای webhook)

### مراحل نصب

1. **کلون کردن پروژه**
```bash
git clone [repository-url]
cd Store-Bot
```

2. **تنظیم پایگاه داده**
- یک پایگاه داده MySQL ایجاد کنید
- اطلاعات اتصال را در `config.php` وارد کنید

3. **تنظیم ربات تلگرام**
- از [@BotFather](https://t.me/botfather) یک ربات جدید ایجاد کنید
- توکن ربات را در `config.php` قرار دهید

4. **تنظیم Webhook**
```bash
curl -X POST "https://api.telegram.org/bot[BOT_TOKEN]/setWebhook" \
     -d "url=https://yourdomain.com/bot.php"
```

5. **تنظیم مجوزها**
```bash
chmod 755 doc/
chmod 666 doc/*.txt
```

## ساختار پروژه

```
Store-Bot/
├── bot.php              # فایل اصلی ربات
├── config.php           # تنظیمات پایگاه داده و متغیرها
├── function.php         # توابع کمکی
├── index.php           # صفحه اصلی
├── doc/                # فایل‌های تنظیمات
│   ├── *.txt           # متون و تنظیمات ربات
│   ├── stats.php       # بررسی سفارشات API
│   ├── sendtoall.php   # ارسال پیام همگانی
│   └── updateApi.php   # بروزرسانی محصولات
├── pay/                # درگاه پرداخت زرین‌پال
│   ├── index.php       # صفحه پرداخت
│   └── back.php        # بازگشت از درگاه
└── zibal/              # درگاه پرداخت زیبال
    ├── index.php       # صفحه پرداخت
    └── back.php        # بازگشت از درگاه
```

## تنظیمات

### متغیرهای اصلی در `config.php`
```php
define('API_TOKEN', 'YOUR_BOT_TOKEN');    // توکن ربات
$_host = "https://yourdomain.com";        // آدرس سایت
$_botUsername = "your_bot_username";      // نام کاربری ربات
$_adminList = ['ADMIN_USER_ID'];          // لیست ادمین‌ها
```

### تنظیم درگاه‌های پرداخت
- **زرین‌پال**: مرچنت کد در `doc/adminZarinToken.txt`
- **زیبال**: مرچنت کد در `doc/adminZibalToken.txt`

## استفاده

### برای کاربران عادی
1. `/start` - شروع کار با ربات
2. **سفارش محصول** - انتخاب دسته‌بندی و محصول
3. **شارژ کیف پول** - افزایش اعتبار حساب
4. **پیگیری سفارش** - بررسی وضعیت سفارشات

### برای مدیران
1. `/panel` - ورود به پنل مدیریت
2. **مدیریت محصولات** - افزودن/ویرایش محصولات
3. **مدیریت کاربران** - مشاهده آمار و مدیریت کاربران
4. **تنظیمات** - پیکربندی ربات

## CRON Jobs

برای عملکرد بهینه، این وظایف را در crontab تنظیم کنید:

```bash
# بررسی سفارشات API هر 5 دقیقه
*/5 * * * * curl -s "https://yourdomain.com/doc/stats.php"

# ارسال پیام‌های همگانی هر دقیقه
* * * * * curl -s "https://yourdomain.com/doc/sendtoall.php"
```

## امنیت

- **محافظت از DDOS**: بررسی IP های تلگرام
- **احراز هویت**: تایید شماره تلفن
- **مجوزهای دسترسی**: سیستم ادمین چندسطحه
- **رمزنگاری**: استفاده از HTTPS برای webhook

## مشارکت

برای مشارکت در پروژه:
1. Fork کنید
2. یک branch جدید ایجاد کنید
3. تغییرات خود را commit کنید
4. Pull Request ارسال کنید

## پشتیبانی

برای دریافت پشتیبانی:
- Issues در GitHub
- تلگرام: [@TheARC](https://t.me/TheARC)

## مجوز

این پروژه تحت مجوز MIT منتشر شده است.

---

**توسعه‌دهنده**: [@TheARC](https://t.me/TheARC)  
**نسخه**: 1.0.0  
**آخرین بروزرسانی**: 2024
