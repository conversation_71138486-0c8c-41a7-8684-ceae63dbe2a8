<?php
include '../function.php';
//>> ----------------------------------------<<\\
//>> دریافت دیتا
$data = array(
'key'=> TOKEN_SITE, // (دریافتی از آذر سوشال (اجباری برای همه عملیات Api      
'action'=>'services', // (عملیات (فقط برای ثبت سفارش اختیاری برای سایر عملیات اجباری
);
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, API_SITE); 
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$result = curl_exec($ch);
$services=json_decode($result,true);
// file_put_contents('results.txt', date("Y/n/d G:i:s").PHP_EOL.$result.PHP_EOL.PHP_EOL,FILE_APPEND);
curl_close($ch);
//>> پاک کردن دیتابیس
mysqli_query($db,"UPDATE product SET checked=0 WHERE api>0");
mysqli_query($db,"UPDATE category SET checked=0 WHERE api=1");
//>> پارامتر های محصول
$i=0;
while($serviceParams=$services[$i]){
$service=$serviceParams['service'];
$name=$serviceParams['name'];
$category=$serviceParams['category'];
$rate=$serviceParams['rate'];
$rate/=1000;
$min=$serviceParams['min'];
$max=$serviceParams['max'];
$type=$serviceParams['type'];
$desc=$serviceParams['desc'];
$dripfeed=$serviceParams['dripfeed'];

//>> بررسی وجود کتگوری
$num=in_array($category,$newCats);
if(!$num){
    $num=mysqli_num_rows(mysqli_query($db,"SELECT * FROM category WHERE name='$category'"));
    if($num==0){
        mysqli_query($db,"INSERT INTO category(name,api,parent,checked) VALUES('$category',1,'$_autoOrders',1)");
        $newCats[]=$category;
    }else{
        mysqli_query($db,"UPDATE category SET checked=1 WHERE name='$category'");
    }
}
//>> ثبت در دیتابیس
$num=mysqli_num_rows(mysqli_query($db,"SELECT * FROM product WHERE name='$name'"));

//>> اگر نبود
if($num==0){
    mysqli_query($db,"INSERT INTO product(name,category,price,min,max,des,api,checked) VALUES('$name','$category','$rate','$min','$max','$desc','$service',1)");
} 
else{
    mysqli_query($db,"UPDATE product SET category='$category' , price='$rate' , min='$min' , max='$max' , des='$desc' , api='$service' , checked=1 WHERE name='$name'");
    
} 
$i++;
}

//>> checked Delete
mysqli_query($db,"DELETE FROM product WHERE checked=0 and api>5");
mysqli_query($db,"DELETE FROM category WHERE checked=0");

//>> اطلاع به کانال
// message(452323199,"done");