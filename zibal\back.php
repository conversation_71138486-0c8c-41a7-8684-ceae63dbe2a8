<?php
include '../function.php';
//==============================================================
$user_id = $_GET['id'];
$success = $_GET['success'];
$plan = $_GET['plan'];
$trackId = $_GET['trackId'];
//==============================================================
$data = array('merchant' => $ZibalMerchantID , 'trackId' => $trackId);
 $jsonData = json_encode($data);
 $ch = curl_init('https://gateway.zibal.ir/v1/verify');
 curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
 curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
 curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
 curl_setopt($ch, CURLOPT_HTTPHEADER, array(
 'Content-Type: application/json',
 'Content-Length: ' . strlen($jsonData)
 ));
 $result = curl_exec($ch);
$err = curl_error($ch);
 curl_close($ch);
 $resultSay=$result;
//  message(452323199,$result);
 $result = json_decode($result, true);
 if ($err) {
 echo "cURL Error #:" . $err;
 }
//==============================================================
if ($result['result'] == '100'){
$refNumber=$result['refNumber'];
$amount=$result['amount'];
$amount=$amount/10;
$cardNumber=$result['cardNumber'];

$user = mysqli_fetch_assoc(mysqli_query($db,"SELECT * FROM `user` WHERE `uid` = '$user_id' LIMIT 1"));
$pluscoin = $user['wallet']  +  $amount;
//>> اطلاع به کاربر
$msg="✅ #خرید شما با موفقیت انجام شد.

💳 مبلغ $amount تومان به حساب شما اضافه شد.

💰 موجودی جدید شما : $pluscoin تومان
";
message($user_id,$msg);

//>> تغییر دیتابیس
$time=time();
mysqli_query($db,"UPDATE user SET wallet='$pluscoin' WHERE uid='$user_id' LIMIT 1");
mysqli_query($db,"INSERT INTO payment(uid,cost,time) VALUES ('$user_id','$amount','$time')");
$username=getChat($user_id)['result']['username'];
mysqli_query($db,"UPDATE user SET username='$username' WHERE uid='$user_id' LIMIT 1");
$phone=$user['phone'];
//>> گزارش به کانال
$msg=" #خرید اعتبار
$cardNumber
📱 $phone
👤 <a href='tg://user?id=".$user_id."'>".$user_id."</a> $username
💵 $amount تومان
💰 $pluscoin تومان

$refNumber
#زیبال";
message($_payReport,$msg);

//>> همکاری

if($user['invitedBy']!==null){
//>> محاسبه پورسانت
$invitedBy=mysqli_fetch_assoc(mysqli_query($db,"SELECT * FROM user WHERE uid='{$user['invitedBy']}' LIMIT 1"));
$inviter=$invitedBy['uid'];
$porsant=round(($amount*$_porsant)/100);
$pluscoin=$invitedBy['wallet']+$porsant;
//>> اطلاع به کاربر
$msg="🎉 #پورسانت خرید زیرمجموعه به شما تعلق گرفت.

👥 مبلغ $porsant تومان به حساب شما اضافه شد.

💰 موجودی جدید شما : $pluscoin تومان

🌐 @$_botUsername";
message($inviter,$msg);

//>> گزارش به کانال
$msg="💳 #پورسانت زیرمجموعه
👤 <a href='tg://user?id=".$inviter."'>".$inviter."</a>
👥 <a href='tg://user?id=".$user_id."'>".$user_id."</a>
💵 $porsant تومان
💰$pluscoin تومان";
message($_payReport,$msg);    
}
//>> تغییرات دیتابیس
mysqli_query($db,"UPDATE user SET wallet='$pluscoin' WHERE uid='$inviter' LIMIT 1");
?>
<html>
	<head>
		<title>افزایش موجودی | <?php echo $_botName;?></title>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
			<meta name="description" content="صفحه افزایش موجودیِ حساب">
      <meta name="keywords" content="افزایش بازدید و لایک پست ها در تلگرام">
		<link rel="stylesheet" href="assets/css/main.css" />
		<link rel="stylesheet" href="assets/css/rtl.css" />
		<link rel="icon" type="image/png" href="images/fav.png">
	</head>
	<body>
		<!-- Wrapper -->
			<div id="wrapper">
				<!-- Header -->
					<header id="header" class="alt">
						<h1>ربات <?php echo $_botName;?></h1>
						<p>صفحه افزایش موجودیِ حساب</p>
					</header>
				<!-- Main -->
					<div id="main">
					<!-- First Section -->
										<section id="intro" class="main">
								<div class="spotlight">
							        <div class="content">
										<header class="major">
											<h2>پرداخت شما با موفقیت انجام شد و خریدتان به حسابتان اعمال شد, برای ادامه به ربات مراجعه کنید .</h2>
										</header>
										<ul class="actions">
											<li><a href="<?php echo "https://t.me/$_botUsername";?>" class="button">برگشت به ربات</a></li>
										</ul>
									</div>
								</div>
							</section>
					</div>		
			</div>
		<!-- Scripts -->
			<script src="assets/js/jquery.min.js"></script>
			<script src="assets/js/jquery.scrollex.min.js"></script>
			<script src="assets/js/jquery.scrolly.min.js"></script>
			<script src="assets/js/skel.min.js"></script>
			<script src="assets/js/util.js"></script>
			<script src="assets/js/main.js"></script>
	</body>
</html>
<?php
}else {
?>
<html>
	<head>
		<title>افزایش موجودی | <?php echo $_botName;?></title>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
			<meta name="description" content="صفحه افزایش موجودیِ حساب">
      <meta name="keywords" content="افزایش بازدید و لایک پست ها در تلگرام">
		<link rel="stylesheet" href="assets/css/main.css" />
		<link rel="stylesheet" href="assets/css/rtl.css" />
		<link rel="icon" type="image/png" href="images/fav.png">
	</head>
	<body>
		<!-- Wrapper -->
			<div id="wrapper">
				<!-- Header -->
					<header id="header" class="alt">
						<h1>ربات <?php echo $_botName;?></h1>
						<p>صفحه افزایش موجودیِ حساب</p>
					</header>
				<!-- Main -->
					<div id="main">
					<!-- First Section -->
										<section id="intro" class="main">
								<div class="spotlight">
							        <div class="content">
										<header class="major">
											<h2>پرداخت شما با موفقیت انجام نشد ! به ربات برگشته و مجدد اقدام به خرید کنید</h2>
										</header>
										<ul class="actions">
											<li><a href="<?php echo "https://t.me/$_botUsername";?>" class="button">برگشت به ربات</a></li>
										</ul>
									</div>
								</div>
							</section>
					</div>		
			</div>
		<!-- Scripts -->
			<script src="assets/js/jquery.min.js"></script>
			<script src="assets/js/jquery.scrollex.min.js"></script>
			<script src="assets/js/jquery.scrolly.min.js"></script>
			<script src="assets/js/skel.min.js"></script>
			<script src="assets/js/util.js"></script>
			<script src="assets/js/main.js"></script>
	</body>
</html>
<?php 
}
?>