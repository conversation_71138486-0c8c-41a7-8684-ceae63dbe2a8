<?php
require 'config.php';
////////////////////////// Functions /////////////////////////
function bot($data){
    return json_decode(file_get_contents("https://api.telegram.org/bot".API_TOKEN."/".$data),true);
}
function message($chat_id,$msg,$markup=null,$reply_to_message_id=null){
    $msg=urlencode($msg);
    return bot("sendMessage?chat_id=$chat_id&text=$msg&disable_web_page_preview=1&reply_markup=$markup&parse_mode=HTML&reply_to_message_id=$reply_to_message_id");
}

function test($msg,$markup=null,$reply_to_message_id=null){
    $msg=urlencode($msg);
    return bot("sendMessage?chat_id=452323199&text=$msg&disable_web_page_preview=1&reply_markup=$markup&parse_mode=HTML&reply_to_message_id=$reply_to_message_id");
}

function forward($user_id,$from_chat_id,$message_id){
    return bot("forwardMessage?chat_id=".$user_id."&from_chat_id=".$from_chat_id."&message_id=".$message_id);
}

function kopy($user_id,$from_chat_id,$message_id,$reply_to_message_id=null){
    return bot("copyMessage?chat_id=$user_id&from_chat_id=$from_chat_id&message_id=$message_id&reply_to_message_id=$reply_to_message_id");
}

function editMessage($chat_id,$message_id,$msg){
    return bot("editMessageText?chat_id=".$chat_id."&message_id=".$message_id."&text=".$msg);
}

function deleteMessage($chat_id,$message_id){
    return bot("deleteMessage?chat_id=".$chat_id."&message_id=".$message_id);
}

function editMessageReplyMarkup($chat_id,$message_id,$markup){
    return bot("editMessageReplyMarkup?chat_id=".$chat_id."&message_id=".$message_id."&reply_markup=".$markup);
}

function answerCallbackQuery($callback_query_id,$text,$show_alert=true){
    $text=urlencode($text);
    return bot("answerCallbackQuery?callback_query_id=".$callback_query_id."&text=".$text."&show_alert=".$show_alert);
}

function isMember($user_id,$chat_id){
    $status=bot("getChatMember?chat_id=".$chat_id."&user_id=".$user_id);
    return $status['result']['status'];
}

function deleteCategory($tbName,$categoryId) {
    global $db;
    $deleteQuery = "DELETE FROM $tbName WHERE id = '$categoryId'";
    mysqli_query($db, $deleteQuery);
    test("DELETE FROM $tbName WHERE id = '$categoryId'");

    // Select subcategories
    $selectQuery = "SELECT id FROM $tbName WHERE parent = '$categoryId'";
    $result = mysqli_query($db, $selectQuery);
    

    if ($result && mysqli_num_rows($result) > 0) {
        while ($row = mysqli_fetch_assoc($result)) {
            $subcategoryId = $row['id'];
            deleteCategory($db, $subcategoryId);
        }
    }
}



function getChat($chat_id){
    return bot("getChat?chat_id=$chat_id");
}

function filtering($filter,$input){
    if($filter=="channel"){ //>> کانال تلگرام
        if(strpos($input, '@')!==false or strpos($input, 't.me/joinchat')!==false) true;
        else false;
    }
    elseif($filter=="post"){ //>> پست تلگرام
        $pure=explode("t.me/",$input)[1];
        $pure=explode("/",$pure);
        $channel=$pure[0];
        $message=$pure[1];
        if(strpos($input, '/c/')!==false) return false;
        if(strpos($input, "t.me")!==false and $channel!==null and $message!==null) return true;
        else return false;
    }elseif($filter=="page"){ //>> پیج اینستا
        if(strpos($input, "@")!==false or strpos($input, "instagram.com/")!==false) return true;
        else return false;
    }elseif($filter=="ipost"){ //>> پست اینستا
        $post=explode("/p/",$input)[1];
        $len=strlen($post);
        if(strpos($input, 'instagram.com/p/')!==false and $len<15) return true;
        else return false;
    }
}

function photo($chat_id,$photo_link,$caption=null,$markup=null)
{
    $caption = urlencode($caption);
    bot("sendPhoto?chat_id=".$chat_id."&photo=".$photo_link."&caption=$caption&reply_markup=$markup&parse_mode=HTML"); 
}
function video($chat_id,$video_link,$caption=null)
{
    bot("sendVideo?chat_id=".$chat_id."&video=".$video_link."&caption=".$caption);
}

function sendFile($chat_id,$file_id,$caption=null)
{
    bot("sendDocument?chat_id=".$chat_id."&document=".$file_id."&caption=".$caption);
}


function setStep($user_id,$step){
    global $db;
    $res=mysqli_query($db,"UPDATE user SET step='$step' WHERE uid='$user_id'");
    return $res;
}

function getWallet($user_id){
    global $db;
    $query="select wallet from user WHERE uid=".$user_id;
    $res=mysqli_query($db, $query);
    $res=mysqli_fetch_assoc($res);
    return $res['wallet'];
}

function setUser($col,$user_id,$string){
    global $db;
    $query="update user set ".$col."='".$string."' WHERE uid=".$user_id;
    $res=mysqli_query($db, $query);
    return $res;
}




?>