<?php

require "../function.php";

$apiid = $_GET['api'];
$addtype = $_GET['type'] == null ? "simple" : $_GET['type'];
$admin = $_GET['admin'];
$percent = $_GET['percent'];
$admin = 452323199;

echo "<pre>";

$api = mysqli_query($db, "SELECT * FROM apis WHERE id = '$apiid'");
if (mysqli_num_rows($api) < 1)
    die;

$api = mysqli_fetch_assoc($api);

$token = $api['token'];
$site = $api['site'];

//>> دریافت دیتا
$data = array(
    'key' => $token,
    // (دریافتی از آذر سوشال (اجباری برای همه عملیات Api      
    'action' => 'services', // (عملیات (فقط برای ثبت سفارش اختیاری برای سایر عملیات اجباری
);
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $site);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$result = curl_exec($ch);
$services = json_decode($result, true);
// file_put_contents('results.txt', date("Y/n/d G:i:s").PHP_EOL.$result.PHP_EOL.PHP_EOL,FILE_APPEND);
curl_close($ch);
//>> پاک کردن دیتابیس
echo "<pre>";
echo "checked = 0\n";
mysqli_query($db, "UPDATE product SET checked=0 WHERE api='$apiid'");
//>> پارامتر های محصول
$i = 0;
while ($serviceParams = $services[$i]) {

    $service = $serviceParams['service'];
    $name = trim($serviceParams['name']);
    $category = $serviceParams['category'];
    $rate = $serviceParams['rate'];
    $rate /= 1000;
    $min = $serviceParams['min'];
    $max = $serviceParams['max'];
    $type = $serviceParams['type'];
    $desc = $serviceParams['desc'];
    $dripfeed = $serviceParams['dripfeed'];

    //>> ثبت در دیتابیس
    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM product WHERE api = '$apiid' AND code = '$service'"));

    //>> اگر نبود
    if ($num == 0 || $addtype == "all") {
        $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM category WHERE name='$category'"));
        if ($num <1) {
            mysqli_query($db, "INSERT INTO category(name,api,parent,checked) VALUES('$category',1,'0',1)");
            echo "INSERT INTO category(name,api,parent,checked) VALUES('$category',1,'0',1)\n";
        }
        $rateNew = round($rate * $percent / 100) + $rate;
        mysqli_query($db, "INSERT INTO product(name,category,price,min,max,des,api,code,checked,old_price) VALUES('$name','$category','$rateNew','$min','$max','$desc','$apiid','$service',1,'$rate')");
        echo "INSERT INTO product(name,category,price,min,max,des,api,code,checked,old_price) VALUES('$name','$category','$rateNew','$min','$max','$desc','$apiid','$service',1,'$rate')\n";
    } else {
        $nowData = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE api='$service'"));
        $oldPrice = $nowData['old_price'];
        $price = $nowData['price'];
        if ($percent > 0) {
            $rateNew = round($rate * $percent / 100) + $rate;
            test("78 $rateNew = round($rate * $percent / 100) + $rate");
        }
        if ($oldPrice < $rate){
            $rateNew += $rate - $oldPrice;
        }
            

        message($admin, "Old product | $name | $service | $rateNew");
        mysqli_query($db, "UPDATE product SET price='$rateNew' , old_price='$rate' , min='$min' , max='$max' , des='$desc' , category = '$category' , checked=1 WHERE api='$apiid' && code='$service'");
        echo "UPDATE product SET price='$rateNew' , old_price='$rate' , min='$min' , max='$max' , des='$desc' , category = '$category' , checked=1 WHERE api='$apiid' && code='$service'\n";
    }
    $i++;
}

//>> checked Delete
mysqli_query($db, "DELETE FROM product WHERE checked=0");
echo "Deleted 0";

//>> اطلاع به کانال
// message(452323199,"done");

?>