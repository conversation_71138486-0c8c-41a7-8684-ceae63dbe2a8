<!DOCTYPE HTML>
<!--
In The Name of God 
Source Of SeenService
By PHP And Mysql
V.2.5
* * * * * * * * * *
Developer : <PERSON><PERSON><PERSON><PERSON>
Telegram : @MohammadRezajiji
Phone : 09392287037
-->
<?php 
//>> فایل های کنترلی
include '../function.php';
//>> دیتای ورودی -------------------<<//
$user = $_GET['id'];
$amount = $_GET['amount'];
$name = bot("getChatMember?chat_id=$user&user_id=$user]")['result']['user']['first_name'];

$status=mysqli_fetch_assoc(mysqli_query($db,"SELECT * FROM user WHERE uid='$user' LIMIT 1"))['status'];
if($status!=1){
    echo "Not Valid!";
    die;
}
if($amount<5000){
    echo "Not Valid!";
    die;
}
//>> دیتای خروجی -------------------<<//
$CallbackURL = "$_host/zibal/back.php?id=$user&amount=$amount"; // لینک برگشت خرید
$Description = "افزایش موجودی ربات $_botName | کاربر: $user";

//>> ساخت لینک -------------------<<//
$data = array('merchant' => $ZibalMerchantID,
 'amount' => $amount*10,
 'callbackUrl' => $CallbackURL,
 'description' => $Description);
$jsonData = json_encode($data);
$ch = curl_init('https://gateway.zibal.ir/v1/request');
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
 'Content-Type: application/json',
 'Content-Length: ' . strlen($jsonData)
));
$result = curl_exec($ch);
$err = curl_error($ch);
// test($result);
$result = json_decode($result, true);
curl_close($ch);
if ($err) {
echo "Something Went Wrong :" . $err;
die;
}

//>> خود لینک -------------------<<//
$pay = "https://gateway.zibal.ir/start/".$result['trackId'];
?>
<html>
	<head>
		<title>افزایش موجودی | <?php echo $_botName;?></title>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
			<meta name="description" content="صفحه افزایش موجودیِ حساب">
      <meta name="keywords" content="افزایش بازدید و لایک پست ها در تلگرام">
		<link rel="stylesheet" href="assets/css/main.css" />
		<link rel="stylesheet" href="assets/css/rtl.css" />
		<link rel="icon" type="image/png" href="images/fav.png">
	</head>
	<body>
		<!-- Wrapper -->
			<div id="wrapper">
				<!-- Header -->
					<header id="header" class="alt">
						<h1>ربات <?php echo $_botName;?></h1>
						<p>صفحه افزایش موجودیِ حساب</p>
					</header>
				<!-- Main -->
					<div id="main">
					<!-- First Section -->
										<section id="intro" class="main">
								<div class="spotlight">
									<div class="content">
										<header class="major">
											<h2>افزایش موجودی برای شناسه <?php echo "'$user'";?></h2>
										</header>
										<h3><?php echo "$name عزیز";?></h3>
										<p>برای افزایش موجودی حساب خود  به مبلغ <?php echo $amount;?> تومان , کافیست از دکمه زیر استفاده کنید تا به صفحه پرداخت مطمئن منتقل شوید و پس از خرید موجودی حساب شما به صورت خودکار افزایش پیدا خواهد کرد</p>
										<ul class="actions">
											<li><a href="<?php echo $pay;?>" class="button">پرداخت <?php echo $amount;?> تومان</a></li>
											<li><a href="<?php echo "https://t.me/$_botUsername";?>" class="button">برگشت به ربات</a></li>
										</ul>
									</div>
								</div>
							</section>
					</div>		
			</div>
		<!-- Scripts -->
			<script src="assets/js/jquery.min.js"></script>
			<script src="assets/js/jquery.scrollex.min.js"></script>
			<script src="assets/js/jquery.scrolly.min.js"></script>
			<script src="assets/js/skel.min.js"></script>
			<script src="assets/js/util.js"></script>
			<script src="assets/js/main.js"></script>
	</body>
</html>