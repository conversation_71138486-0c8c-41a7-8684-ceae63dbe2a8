<?php
/**
 * Store Bot Configuration File
 * تنظیمات اصلی ربات فروشگاه
 *
 * لطفا تمامی مقادیر زیر را با اطلاعات واقعی خود جایگزین کنید
 */

//<<----------------- Database Configuration ----------------->>\\
global $db;

// تنظیمات پایگاه داده - Database Settings
$db_host = 'localhost';                    // آدرس سرور پایگاه داده
$db_username = 'DB_USERNAME';              // نام کاربری پایگاه داده
$db_password = 'DB_PASSWORD';              // رمز عبور پایگاه داده
$db_name = 'DB_NAME';                      // نام پایگاه داده

$db = mysqli_connect($db_host, $db_username, $db_password, $db_name);
if (!$db) {
    die('خطا در اتصال به پایگاه داده: ' . mysqli_connect_error());
}

// تنظیم کاراکتر UTF-8 برای پشتیبانی از زبان فارسی
mysqli_query($db, "SET NAMES 'utf8mb4'");
mysqli_query($db, "SET CHARACTER SET utf8mb4");
mysqli_query($db, "SET SESSION collation_connection = 'utf8mb4_unicode_ci'");

//<<----------------- Bot Configuration ----------------->>\\

//>> توکن ربات تلگرام - Telegram Bot Token
// از @BotFather دریافت کنید
define('API_TOKEN', 'BOT_TOKEN');

//>> آدرس سایت - Website URL
// آدرس کامل سایت شما (بدون / در انتها)
$_host = "https://yourdomain.com";

//>> نام کاربری ربات - Bot Username (بدون @)
$_botUsername = "YOUR_BOT_USERNAME";

//>> نام ربات - Bot Display Name
$_botName = "نام ربات شما";

//>> آیدی ادمین اصلی - Main Admin User ID
// آیدی تلگرام ادمین اصلی (عدد)
$_mainAdmin = 'MAIN_ADMIN_USER_ID';

//<<----------------- Business Settings ----------------->>\\

//>> درصد کمیسیون - Commission Percentage
$_porsant = 5;

//>> توکن پیامک - SMS Token
// در صورت استفاده از سرویس پیامک
$_smsToken = "SMS_TOKEN";

//<<----------------- Payment Gateway Settings ----------------->>\\

//>> مرچنت زرین‌پال - ZarinPal Merchant ID
$MerchantID = "ZARINPAL_MERCHANT_ID";

// نکته: مرچنت زیبال از فایل doc/adminZibalToken.txt خوانده می‌شود

//<<----------------- Dynamic Settings ----------------->>\\
// این تنظیمات از فایل‌های doc خوانده می‌شوند و از طریق پنل ادمین قابل تغییر هستند

//>> پیام شروع
$_startMsg = file_get_contents("doc/adminStartText.txt");

//>> کانال‌های گزارش
$_orderReport = file_get_contents("doc/adminReportChannel.txt");
$_apiOrderReport = file_get_contents("doc/adminAPIReportChannel.txt");
$_payReport = file_get_contents("doc/adminPaymentChannel.txt");
$_ticketReport = file_get_contents("doc/adminTicketChannel.txt");

//>> کانال اسپانسر (عضویت اجباری)
$_sponsor = file_get_contents("doc/adminJoinChannel.txt");

//>> لیست ادمین‌ها
$_adminList = [$_mainAdmin]; // ادمین اصلی
$adminFile = file_get_contents("doc/admins.txt");
if (!empty($adminFile)) {
    $_adminList = array_merge($_adminList, explode("_", $adminFile));
    $_adminList = array_unique($_adminList);
}

//<<----------------- CRON Jobs Information ----------------->>\\
/*
برای عملکرد صحیح ربات، لطفا CRON Jobs زیر را تنظیم کنید:

1. بررسی سفارشات API (هر 5 دقیقه):
   0/5 * * * * curl -s "https://yourdomain.com/doc/stats.php"

2. ارسال پیام‌های همگانی (هر دقیقه):
   0 * * * * curl -s "https://yourdomain.com/doc/sendtoall.php"

3. بروزرسانی محصولات (اختیاری - هر ساعت):
   0 * * * * curl -s "https://yourdomain.com/doc/updateApi.php"
*/

//<<----------------- Security Settings ----------------->>\\

// فعال‌سازی نمایش خطاها (فقط در محیط توسعه)
// در محیط تولید این مقدار را false کنید
ini_set('display_errors', 0);

// تنظیم منطقه زمانی
date_default_timezone_set("Asia/Tehran");

//<<----------------- End of Configuration ----------------->>\\
