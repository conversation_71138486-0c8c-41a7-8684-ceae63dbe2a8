<?php
unlink("error_log");
// die;
// کرون جاب هر دقیقه یک بار فعال شود
include '../function.php'; // سازگار با سورس های خودم
$admin = ['452323199','19723908','88003993'];
ini_set('display_errors', 0);
//=======================================================
$send = mysqli_fetch_assoc(mysqli_query($db,"SELECT * FROM `sendall` LIMIT 1"));
$chat=$send['chat'];
$msg=$send['text'];
//======================// send //=======================
if($send['step'] == 'send'){
$alluser = mysqli_num_rows(mysqli_query($db,"SELECT 'uid' from `user`"));
$users = mysqli_query($db,"SELECT * FROM `user` LIMIT 50 OFFSET {$send['user']}");
while($row = mysqli_fetch_assoc($users)){
    $useresh=$row['uid'];
    kopy("$useresh",$chat,$msg);
}
message(452323199,"$useresh");
$db->query("UPDATE `sendall` SET `user` = `user` + 50 LIMIT 1");
if($send['user'] + 50 >= $alluser){
 message($admin[0],"📢 پیام همگانی برای همه کاربران ارسال شد.");
$db->query("UPDATE `sendall` SET `step` = 'none' , `text` = '' , `user` = '0' , `chat` = '' LIMIT 1");	
}
}
//======================// forward //=======================
elseif($send['step'] == 'forward'){
$alluser = mysqli_num_rows(mysqli_query($db,"SELECT uid from `user`"));
$users = mysqli_query($db,"SELECT uid FROM `user` LIMIT 50 OFFSET {$send['user']}");
while($row = mysqli_fetch_assoc($users)){
    $useresh=$row['uid'];
    forward($row['uid'],$send['chat'],$send['text']);
}
$db->query("UPDATE `sendall` SET `user` = `user` + 50 LIMIT 1");
if($send['user'] + 50 >= $alluser){
message($admin[0],"📢 پیام همگانی برای همه کاربران ارسال شد.");
$db->query("UPDATE `sendall` SET `step` = 'none' , `text` = '' , `user` = '0' , `chat` = '' LIMIT 1");		
}
}
?> 