<?php  
                    
require "../function.php";

//>> گرفتن پارامترها
$token=$_GET['token'];
$limit=$_GET['limit'];
$offset=$_GET['offset'];
$time=$_GET['time'];
$name=$_GET['name'];

if($limit==0 or $limit==null) $limit=1000;
if($offset==0 or $offset==null) $offset=0;
if($time==0 or $time==null) $time=0;

if($token!="4SAF4dsaf4DSAF584asdfasd") die;


$setSql = "SELECT * FROM `orders` WHERE time>$time ORDER BY `id` DESC LIMIT $limit OFFSET $offset";  
test("SELECT * FROM `orders` WHERE time>$time ORDER BY `id` DESC LIMIT $limit OFFSET $offset");
$setRec = mysqli_query($db, $setSql);  
  
$setRecHeader=$setRec;

$columnHeader = '';

$colQuery=mysqli_query($db,"SELECT `COLUMN_NAME` FROM `INFORMATION_SCHEMA`.`COLUMNS` WHERE `TABLE_SCHEMA` = 'anghaair_testDataBase' AND `TABLE_NAME` = 'orders'");
while($col=mysqli_fetch_assoc($colQuery)['COLUMN_NAME']){
    $columnHeader=$columnHeader."$col"."\t";
}
// $columnHeader = "id" . "\t" . "name" . "\t" . "parentName" . "\t" . "proje" . "\t" . "snsh" . "\t" . "quantity" . "\t" . "date" . "\t" . "shamsi" . "\t" . "type" . "\t" . "kitchen" . "\t" . "fromwho" . "\t" . "status" . "\t";  
  
$setData = '';  
  
while ($rec = mysqli_fetch_row($setRec)) {  
    $rowData = '';  
    foreach ($rec as $value) {  
        $value = '"' . $value . '"' . "\t";  
        $rowData .= $value;  
    }  
    $setData .= trim($rowData) . "\n";  
}  
  
 
header("Content-type: application/octet-stream");  
header("Content-Disposition: attachment; filename=$name.xls");  
header('Content-Transfer-Encoding: binary');
header("Pragma: no-cache");  
header("Expires: 0");  

echo chr(255).chr(254).iconv("UTF-8", "UTF-16LE//IGNORE", $columnHeader . "\n" . $setData . "\n");

exit()

?>