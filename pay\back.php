<?php
include '../bot.php';
//==============================================================
$user_id = $_GET['id'];
$amount = $_GET['amount'];
$Authority = $_GET['Authority'];
$MerchantID = file_get_contents("../doc/adminZarinToken.txt");

//==============================================================
$client = new SoapClient('https://www.zarinpal.com/pg/services/WebGate/wsdl', ['encoding' => 'UTF-8']);
$result = $client->PaymentVerification([
'MerchantID' => $MerchantID,
'Authority' => $Authority,
'Amount' => $amount,
]);
//==============================================================
if ($_GET['Status'] == 'OK' and $result->Status == 100){
$refNumber=$result->RefID;
$user = mysqli_fetch_assoc(mysqli_query($db,"SELECT * FROM `user` WHERE `uid` = '$user_id' LIMIT 1"));
$pluscoin = $user['wallet']  +  $amount;
//>> اطلاع به کاربر
$msg="✅ #خرید شما با موفقیت انجام شد.

💳 مبلغ $amount تومان به حساب شما اضافه شد.

💰 موجودی جدید شما : $pluscoin تومان

🌐 @$_botUsername";
message($user_id,$msg,$mainMarkup);

//>> تغییر دیتابیس
$time=time();
mysqli_query($db,"UPDATE user SET wallet='$pluscoin' WHERE uid='$user_id' LIMIT 1");
mysqli_query($db,"INSERT INTO payment(uid,cost,time) VALUES ('$user_id','$amount','$time')");
$username=getChat($user_id)['result']['username'];
mysqli_query($db,"UPDATE user SET username='$username' WHERE uid='$user_id' LIMIT 1");
$phone=$user['phone'];
//>> گزارش به کانال
$msg="💳 #خرید اعتبار
📱 $phone
👤 <a href='tg://user?id=".$user_id."'>".$user_id."</a> $username
💵 $amount تومان
💰 $pluscoin تومان
$refNumber
#زرینپال";
$_payReport = file_get_contents("../doc/adminPaymentChannel.txt");
message($_payReport,$msg);

//>> همکاری

if($user['invitedBy']!==null){
//>> محاسبه پورسانت
$invitedBy=mysqli_fetch_assoc(mysqli_query($db,"SELECT * FROM user WHERE uid='{$user['invitedBy']}' LIMIT 1"));
$inviter=$invitedBy['uid'];
$porsant=round(($amount*$_porsant)/100);
$pluscoin=$invitedBy['wallet']+$porsant;
//>> اطلاع به کاربر
$msg="🎉 #پورسانت خرید زیرمجموعه به شما تعلق گرفت.

👥 مبلغ $porsant تومان به حساب شما اضافه شد.

💰 موجودی جدید شما : $pluscoin تومان

🌐 @$_botUsername";
message($inviter,$msg);

$get = file_get_contents("../doc/adminAfterPayText.txt");
message($user_id, $get, $markup);

//>> گزارش به کانال
$msg="💳 #پورسانت زیرمجموعه
👤 <a href='tg://user?id=".$inviter."'>".$inviter."</a>
👥 <a href='tg://user?id=".$user_id."'>".$user_id."</a>
💵 $porsant تومان
💰$pluscoin تومان";
message($_payReport,$msg);    
}
//>> تغییرات دیتابیس
mysqli_query($db,"UPDATE user SET wallet='$pluscoin' WHERE uid='$inviter' LIMIT 1");

?>
<html>
	<head>
		<title>افزایش موجودی | <?php echo $_botName;?></title>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
			<meta name="description" content="صفحه افزایش موجودیِ حساب">
      <meta name="keywords" content="افزایش بازدید و لایک پست ها در تلگرام">
		<link rel="stylesheet" href="assets/css/main.css" />
		<link rel="stylesheet" href="assets/css/rtl.css" />
		<link rel="icon" type="image/png" href="images/fav.png">
	</head>
	<body>
		<!-- Wrapper -->
			<div id="wrapper">
				<!-- Header -->
					<header id="header" class="alt">
						<h1>ربات <?php echo $_botName;?></h1>
						<p>صفحه افزایش موجودیِ حساب</p>
					</header>
				<!-- Main -->
					<div id="main">
					<!-- First Section -->
										<section id="intro" class="main">
								<div class="spotlight">
							        <div class="content">
										<header class="major">
											<h2>پرداخت شما با موفقیت انجام شد و موجودی خریداری شده به حساب شما افزوده شد , برای ادامه به ربات مراجعه کنید .</h2>
										</header>
										<ul class="actions">
											<li><a href="<?php echo "https://t.me/$user_idnamebot";?>" class="button">برگشت به ربات</a></li>
										</ul>
									</div>
								</div>
							</section>
					</div>		
			</div>
		<!-- Scripts -->
			<script src="assets/js/jquery.min.js"></script>
			<script src="assets/js/jquery.scrollex.min.js"></script>
			<script src="assets/js/jquery.scrolly.min.js"></script>
			<script src="assets/js/skel.min.js"></script>
			<script src="assets/js/util.js"></script>
			<script src="assets/js/main.js"></script>
	</body>
</html>
<?php 
} else {
?>
<html>
	<head>
		<title>افزایش موجودی | <?php echo $_botName;?></title>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
			<meta name="description" content="صفحه افزایش موجودیِ حساب">
      <meta name="keywords" content="افزایش بازدید و لایک پست ها در تلگرام">
		<link rel="stylesheet" href="assets/css/main.css" />
		<link rel="stylesheet" href="assets/css/rtl.css" />
		<link rel="icon" type="image/png" href="images/fav.png">
	</head>
	<body>
		<!-- Wrapper -->
			<div id="wrapper">
				<!-- Header -->
					<header id="header" class="alt">
						<h1>ربات <?php echo $_botName;?></h1>
						<p>صفحه افزایش موجودیِ حساب</p>
					</header>
				<!-- Main -->
					<div id="main">
					<!-- First Section -->
										<section id="intro" class="main">
								<div class="spotlight">
							        <div class="content">
										<header class="major">
											<h2>پرداخت شما با موفقیت انجام نشد ! به ربات برگشته و مجدد اقدام به خرید کنید</h2>
										</header>
										<ul class="actions">
											<li><a href="<?php echo "https://t.me/$_botUsername";?>" class="button">برگشت به ربات</a></li>
										</ul>
									</div>
								</div>
							</section>
					</div>		
			</div>
		<!-- Scripts -->
			<script src="assets/js/jquery.min.js"></script>
			<script src="assets/js/jquery.scrollex.min.js"></script>
			<script src="assets/js/jquery.scrolly.min.js"></script>
			<script src="assets/js/skel.min.js"></script>
			<script src="assets/js/util.js"></script>
			<script src="assets/js/main.js"></script>
	</body>
</html>
<?php 
}
?>