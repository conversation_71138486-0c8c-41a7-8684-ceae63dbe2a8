<?php

/*
=================================================================
                    TELEGRAM STORE BOT
=================================================================
Successful Indeed Are The Believers: 1:23
... Those Who Avoid Idle Talk. 3:23
- The Great Q.
These Are -Mostly- Coded By @TheARC

Description: Complete Telegram Bot for Online Store Management
Features: Product Management, Payment Gateway, Admin Panel, API Integration
Version: 1.0.0
=================================================================
*/
//=================================================================
//                    INITIALIZATION SECTION
//=================================================================
// unlink("error_log");
require 'function.php';
require 'nowpayments_config.php';
//require_once 'doc/jdf.php';
global $db;
date_default_timezone_set("Asia/Tehran");
$input = file_get_contents("php://input");
/*file_put_contents('result.txt', $input.PHP_EOL.PHP_EOL,FILE_APPEND);*/
$update = json_decode($input, true);
$api_url = "https://api.telegram.org/bot" . API_TOKEN . "/";
ini_set('display_errors', 0);
// $smsPanel = file_get_contents("doc/smspanel.txt");
//=================================================================
//                    SECURITY & DDOS PROTECTION
//=================================================================
if (isset($update)) {
    $telegram_ip_ranges = [
        ['lower' => '*************', 'upper' => '***************'],
        ['lower' => '**********', 'upper' => '************'],
    ];
    $ip_dec = (float) sprintf("%u", ip2long($_SERVER['REMOTE_ADDR']));
    $ok = false;
    foreach ($telegram_ip_ranges as $telegram_ip_range)
        if (!$ok) {
            $lower_dec = (float) sprintf("%u", ip2long($telegram_ip_range['lower']));
            $upper_dec = (float) sprintf("%u", ip2long($telegram_ip_range['upper']));
            if ($ip_dec >= $lower_dec and $ip_dec <= $upper_dec)
                $ok = true;
        }
    if (!$ok)
        die();
}

//=================================================================
//                    TELEGRAM UPDATE HANDLING
//=================================================================
if (array_key_exists('message', $update)) {
    $message = $update['message'];
    $forward_from = $update['message']['forward_from']['id'];
    $user_id = $update['message']['from']['id'];
    $chat_id = $update['message']['chat']['id'];
    $message_id = $update['message']['message_id'];
    $username = (array_key_exists('username', $update['message']['from'])) ? $update['message']['from']['username'] : null;
    $last_name = (array_key_exists('last_name', $update['message']['from'])) ? $update['message']['from']['last_name'] : null;
    $first_name = $update['message']['from']['first_name'];
    $text = $update['message']['text'];
    $audio = (array_key_exists('audio', $update['message'])) ? $update['message']['audio']['file_id'] : null;
    $caption = $update['message']['caption'];
    $phone = $update['message']['contact']['phone_number'];
    $contactId = $update['message']['contact']['user_id'];
    $user = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM user WHERE uid='$user_id' LIMIT 1"));
} elseif (array_key_exists('callback_query', $update)) {
    $callback_id = $update['callback_query']['id'];
    $user_id = $update['callback_query']['from']['id'];
    $chat_id = $update['callback_query']['message']['chat']['id'];
    $message_id = $update['callback_query']['message']['message_id'];
    $username = $update['callback_query']['from']['username'];
    $first_name = $update['callback_query']['from']['first_name'];
    $text = $update['callback_query']['data'];
    $data = $text;
    $user = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM user WHERE uid='$user_id' LIMIT 1"));
    ;
}



$text = str_replace(['`', "'",], null, $text);
// test("salam");
//=================================================================
//                    BOT STATUS CHECK
//=================================================================
if (!(file_get_contents("doc/adminBotStatus.txt") == "on" || in_array($user_id, $_adminList))) {
    message($user_id, "ربات در حال آپدیت می باشد. لطفا کمی صبر کنید");
    setStep($user_id, 'home');
    die;
}

$time = time();
//=================================================================
//                    BUTTONS & KEYBOARDS DEFINITION
//=================================================================
$back = "بازگشت 🔙";
$hasJoined = "✅ عضو شدم.";

$quantityChange = "♻️ تغییر تعداد درخواستی";
$reorderLink = "💎 تکرار سفارش با تغییر لینک";
$reorderQuantity = "♻️ تکرار سفارش با تغییر تعداد";

$reorderstatus = "♻️ پیگیری سفارش";

$parsButton = "💵 پرداخت از طریق پارس پی";
$zarinButton = "💵 پرداخت از طریق زرین پال";
$zibalButton = "💵 پرداخت از طریق زیبال";
$cryptoButton = "💰 پرداخت از طریق ارز دیجیتال";
$phoneErsal = "📱 ارسال شماره تماس";
$phoneReErsal = "📱 ارسال مجدد";

$answer = "📬 پاسخ به کاربر";
$orderCheck = "❓ وضعیت سفارش دلخواه";

$support = "📬 پشتیبانی";
$order = "🛍 ثبت سفارش";
$autoOrder = "🚀 ثبت سفارش خودکار";
$autoList = "🚀 سفارش های خودکار فعال";
$info = "👤 اطلاعات حساب";
$cash = "💳 شارژ حساب";
$friend = "💶 همکاری در فروش";
$rules = "🛂 قوانین";
$atm = "🏧 انتقال موجودی";
$plus = "💳 پرداخت آنلاین";
$list = "📊 قیمت خدمات";
$history = "💾 پیگیری";
$carttocart = "💳 کارت به کارت";
$excelButton = "🗂 دریافت اکسل";
$agency = "🛍 خرید ربات نمایندگی";

$mainMarkup = json_encode(
    array(
        'keyboard' => array(
            array($order),
            array($autoList, $autoOrder),
            array($cash, $list),
            array($support, $info),
            array($history, $atm),
            //array($agency),
        ),
        'resize_keyboard' => true
    )
);

$adminEdit = "⚙️ ویرایش محصول";
$adminUserInfo = "⚙️ اطلاعات کاربر";
$adminCatDel = "⚙️ حذف کتگوری";
$adminCatNew = "⚙️ دسته بندی جدید";
$adminProduct = "⚙️ محصول جدید";
$adminEditCat = "⚙️ ویرایش دسته بندی";
$adminCategory = "⚙️ دسته بندی ها";
$adminSend = "⚙️ پیام همگانی";
$adminBlock = "⚙️ مسدود کردن کاربر";
$adminGive = "⚙️ شارژ کاربر";
$supdate = "⚙️ به روز رسانی";
$manual = "⚙️ نرخ دلخواه";
$unmanual = "⚙️ حذف نرخ دلخواه";
$adminAPI = "⚙️ لیست وبسرویس ها";
$adminNewAPI = "⚙️ وبسرویس جدید";
$adminReportChannel = "⚙️ کانال دستی";
$adminPaymentChannel = "⚙️ کانال پرداختی ها";
$adminTicketChannel = "⚙️ کانال تیکت ها";
$adminAPIReportChannel = "⚙️ کانال API ها";
$adminAddFromAPI = "⚙️ محصول تکی از وبسرویس";
$adminJoinChannel = "⚙️ کانال اطلاع رسانی";
$adminCarttocart = "⚙️ متن کارت به کارت";
$adminAgency = "⚙️ متن نمایندگی";
$adminAdmin = "⚙️ مدیریت ادمین ها";
$adminStartText = "⚙️ متن استارت";
$adminAfterPayText = "⚙️ متن بعد از پرداخت";
$adminAfterOrderText = "⚙️ متن بعد از سفارش";
$adminStatistic = "⚙️ آمار ربات";
$adminApisBalance = "⚙️ موجودی وبسرویس ها";
$adminSupport = "⚙️ پشتیبانی";


$adminMainProducts = "⚙️ محصول و دسته بندی";
$adminMainAPI = "⚙️ وبسرویس ها";
$adminMainTexts = "⚙️ متن ها";
$adminChannels = "⚙️ کانال ها";
$adminMainUsers = "⚙️ کاربران ربات";

$adminSetting = "⚙️ تنظیمات";
$adminBotStatus = file_get_contents("doc/adminBotStatus.txt") == "on" ? "خاموش کردن ربات" : "روشن کردن ربات";

$adminGateway = "⚙️ درگاه";
$adminZarinToken = "مرچنت زرین پال";
$adminZarinOnOff = file_get_contents("doc/adminZarinOnOff.txt") == "on" ? "خاموش کردن زرین پال" : "روشن کردن زرین پال";
$adminZibalToken = "مرچنت زیبال";
$adminZibalOnOff = file_get_contents("doc/adminZibalOnOff.txt") == "on" ? "خاموش کردن زیبال" : "روشن کردن زیبال";


$adminBack = "⚙️ بازگشت";

$adminBackMarkup = json_encode(array('keyboard' => array(array($adminBack)), 'resize_keyboard' => true));

$adminMainProductsMarkup = json_encode(
    array(
        'keyboard' => array(
            array($adminCatDel, $adminCatNew),
            array($adminEdit, $adminProduct),
            array($adminAddFromAPI, $adminEditCat),
            array($adminBack)
        ),
        'resize_keyboard' => true
    )
);

$adminMainAPIMarkup = json_encode(
    array(
        'keyboard' => array(
            array($adminAPI, $adminNewAPI),
            array($adminApisBalance, $adminAddFromAPI),
            array($adminBack)
        ),
        'resize_keyboard' => true
    )
);

$adminMainSettingMarkup = json_encode(
    array(
        'keyboard' => array(
            array($adminAdmin),
            array($adminBotStatus),
            array($adminBack)
        ),
        'resize_keyboard' => true
    )
);

$adminMainTextsMarkup = json_encode(
    array(
        'keyboard' => array(
            array($adminCarttocart),
            array($adminStartText, $adminSupport),
            array($adminAfterPayText, $adminAfterOrderText),
            array($adminBack)
        ),
        'resize_keyboard' => true
    )
);

$adminMainChannelsMarkup = json_encode(
    array(
        'keyboard' => array(
            array($adminReportChannel, $adminPaymentChannel),
            array($adminTicketChannel, $adminAPIReportChannel),
            array($adminJoinChannel),
            array($adminBack)
        ),
        'resize_keyboard' => true
    )
);

$adminMainUsersMarkup = json_encode(
    array(
        'keyboard' => array(
            array($adminStatistic),
            array($manual, $unmanual),
            array($adminGive, $adminUserInfo),
            array($adminSend, $adminBlock),
            array($adminBack)
        ),
        'resize_keyboard' => true
    )
);

$adminMarkup = json_encode(
    array(
        'keyboard' => array(
            array($adminMainUsers, $adminSend),
            array($adminMainAPI, $adminGateway),
            array($adminMainProducts, $adminSetting),
            array($adminMainTexts, $adminChannels),
            array($back)
        ),
        'resize_keyboard' => true
    )
);

$adminMainGatewayMarkup = json_encode(
    array(
        'keyboard' => array(
            array($adminZarinOnOff, $adminZarinToken),
            array($adminZibalOnOff, $adminZibalToken),
            array($adminBack)
        ),
        'resize_keyboard' => true
    )
);

$adminNoAPI = "محصول وبسرویس ندارد.";

$apiUpdateAll = "⚙️ بروزرسانی کلی";
$apiUpdate = "⚙️ بروزرسانی فعال ها";
$apiDeleteAll = "⚙️ حذف با محصولات";
$apiDelete = "⚙️ حذف بدون محصولات";

$apiMarkup = json_encode(
    array(
        'keyboard' => array(
            array($apiUpdateAll, $apiUpdate),
            array($apiDeleteAll, $apiDelete),
            array($adminBack)
        ),
        'resize_keyboard' => true
    )
);


$dontWant = "❌ نه نمیخام!";
$addCat = "➕ اضافه کردن کتگوری";
$delCat = "🗑 حذف کتگوری";
$bacCat = "⤴️ بازگشت به قبل";

$startetela = "⌛️ اطلاع شروع سفارش ⌛️";
$endetela = "✅ اطلاع پایان سفارش ✅";
$canceletela = "❌ لغو سفارش و اطلاع به کاربر ❌";

$startetelad = "📬 شروع سفارش به اطلاع کاربر رسید! 📬";
$endetelad = "💌 پایان سفارش به اطلاع کاربر رسید! 💌";
$cancetelad = "🆑 لغو سفارش به اطلاع کاربر رسید 🆑";

$mahsoolInlineButton = "- محصول -";
$priceInlineButton = "- قیمت -";

$editProduct = "کلی";
$deleteProduct = "🗑 حذف";
$editProductCategory = "🗄 تغییر دسته بندی";
$offProduct = "🟡 خاموش کردن محصول";
$onProduct = "🟢 روشن کردن محصول";
$editProductDescription = "توضیحات";
$editProductPrice = "قیمت";
$editProductApi = "API";
$editProductApicode = "ایدی سرویس";
$editProductRange = "حداقل/حداکثر";
$editProductName = "اسم";

$tenLastPayment = "10 پرداخت اخیر";

$backMarkup = json_encode(array('keyboard' => array(array($back)), 'resize_keyboard' => true));
if (in_array($user_id, $_adminList)) {
    $mainMarkup = json_decode($mainMarkup, true);
    $mainMarkup['keyboard'][] = array('پنل');
    $mainMarkup = json_encode($mainMarkup);
}
$rulesMarkup = json_encode(array('keyboard' => array(array($cashRule, $orderRule), array($supportRule, $friendRule), array($back)), 'resize_keyboard' => true));
$friendMarkup = json_encode(array('keyboard' => array(array($friendLink, $friendAgency), array($apiDoc, $friendProduct), array($back)), 'resize_keyboard' => true));
$bacCatMarkup = json_encode(array('keyboard' => array(array($back, $bacCat)), 'resize_keyboard' => true));
$orderCheckMarkup = json_encode(array('keyboard' => array(array($orderCheck), array($tenLastPayment), array($back)), 'resize_keyboard' => true));
$removeMarkup = json_encode(array('remove_keyboard' => true));
$joinedMarkup = json_encode(array('inline_keyboard' => array(array(array('text' => $hasJoined, 'callback_data' => "/start")))));
$phoneMarkup = json_encode(array('keyboard' => array(array(array('text' => $phoneErsal, 'request_contact' => true)), array($back)), 'resize_keyboard' => true));
$phoneNotMarkup = json_encode(array('keyboard' => array(array(array('text' => $phoneReErsal, 'request_contact' => true)), array($back)), 'resize_keyboard' => true));

//=================================================================
//                    AUTO ORDER SYSTEM
//=================================================================

if (array_key_exists('channel_post', $update)) {
    //>> بررسی وجود کانال
    $channel_chat_id = $update['channel_post']['chat']['id'];
    $chusername = $update['channel_post']['chat']['username'];
    $chname = $update['channel_post']['chat']['title'];
    $query = "SELECT * FROM autoorders WHERE cid='$channel_chat_id'";
    $orderdatas = mysqli_query($db, $query);
    $num = mysqli_num_rows($orderdatas);
    if ($num !== 0) {
        while ($orderdata = mysqli_fetch_assoc($orderdatas)) {

            $user_id = $orderdata['uid'];
            $user = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM user WHERE uid='$user_id' LIMIT 1"));

            $quantity = $orderdata['quantity'];
            $pid = $orderdata['pid'];

            $msg_id = $update['channel_post']['message_id'];
            $channel = $update['channel_post']['chat']['username'];
            $channelid = $update['channel_post']['chat']['id'];
            if ($channel == null) {
                die();
            }
            $text = "https://t.me/$channel/$msg_id";



            $status = 0;
            //>> محصول انتخابی کاربر
            $pro = $user['datash'];
            $name = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE id = '$pid'"))['name'];
            //>> دیتای کاربر
            mysqli_query($db, "UPDATE user SET datash='$name' WHERE uid='$user_id' LIMIT 1");
            $pro = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE id='$pid' AND status = 1"));
            $description = $pro['des'];
            $code = $pro['id'];
            $id = $code;
            $papi = $pro['api'];
            $apicode = $pro['code'];
            $doChannel = $pro['doChannel'];
            $price = $pro['price'];

            //>> بررسی قیمت دستی
            $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$pid'"));
            if ($num > 0) {
                $price = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$pid'"))['cost'];
            }
            //>> بررسی موجودی کیف پول
            $cost = $price * $quantity;
            $wallet = $user['wallet'];
            if ($wallet < $cost) {
                $gotowallet = $cost - $wallet;
                $maxAvb = floor($wallet / $price);
                $msg = "❌ موجودی شما برای ثبت خودکار این سفارش کم است.

🌀 موجودی شما $wallet تومان 
💸 هزینه این سفارش $cost تومان 

➕ برای افزایش اعتبار به میزان $gotowallet تومان (شارژ اضافه ی حداقل برای ثبت این سفارش) میتوانید از دستور پایین استفاده کنید:

/charge_$gotowallet

🛂 همچنین میتوانید با توجه به موجودی کیف پولتان حداکثر تعداد $maxAvb عدد و یا مقدار کمتری از این محصول را سفارش دهید:";
                message($user_id, $msg);
                setStep($user_id, 'orderQuantity');
                die;
            }
            //>> ثبت سفارش
            $time = time();
            $timeTell = date("Y/m/d", $time);
            $wallet = $wallet - $cost;
            $peygiri = substr(str_shuffle("1234567890qwertyuiopasdfghjklzxcvbnm"), 4, 10);

            $peygiri = rand(1000000000, 9999999999);
            while (mysqli_num_rows(mysqli_query($db, "SELECT * FROM orders WHERE track = '$peygiri'")) > 0) {
                $peygiri = rand(1000000000, 9999999999);
            }

            //>> بررسی وبسرویس
            $ordersAPIStatus = 0;

            //--------> API <--------\\
            if ($papi > 0) {
                $ordersAPIStatus = 1;
                $api = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM apis WHERE id = '$papi'"));

                $token = $api['token'];
                $site = $api['site'];
                $apiname = $api['name'];

                $data = array(
                    'key' => $token,
                    'action' => 'add',
                    'link' => $text,
                    'quantity' => $quantity,
                    'service' => $apicode,
                );
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $site);
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                $result = curl_exec($ch);
                
                // $result = file_get_contents("$site?key=$token&action=add&link=$text&quantity=$quantity&service=$apicode");
                $peygiri1 = json_decode($result, true);
                $peygiri = $peygiri1['order'];
                curl_close($ch);
                if ($peygiri1['order'] == false) {
                    file_put_contents('error.txt', date("Y/n/d G:i:s") . PHP_EOL . $result . "

⚙️ کد وبسرویس : $apiname | $papi | $apicode
$name
🛍 تعداد : $quantity
🔗 لینک : $text


" . PHP_EOL . PHP_EOL, FILE_APPEND);
                }

                //>> بررسی انجام خودکار سفارش
                if ($peygiri == null) {
                    //>> اطلاع به کاربر
                    $msg = "❌ مشکلی در ثبت سفارش به وجود آمده است!

لطفا چند دقیقه بعد مجددا تلاش کنید و یا به پشتیبانی ربات اطلاع دهید.";
                    message($user_id, $msg, $mainMarkup);
                    setStep($user_id, "home");
                    //>> اطلاع به کانال
                    $msg = "❌  #ارور وبسرویس

👤 <a href='tg://user?id=" . $user_id . "'>" . $user_id . "</a>

⚙️ کد وبسرویس : $apiname | $papi | $apicode
$name
🛍 تعداد : $quantity
🔗 لینک : $text

";
                    message($_orderReport, $msg);
                    message($_orderReport, $result);
                    
                    message(452323199, $msg);
                    message(452323199, $result);
                    die;
                }

            }

            //>> اطلاع به کاربر
            $msg = "<b>✅ سفارش خودکار شما با شناسه <code>$peygiri</code> با موفقیت ثبت شد.

▫ نام محصول انتخابی: </b>$name
<b>▫ تعداد: </b>$quantity<b> عدد
▫ لینک سفارش : </b>$text
<b>🕰 $timeTell
▫ کد پیگیری:</b> <code>$peygiri</code><b>

💵 هزینه سفارش: </b>$cost<b> تومان
💰 موجودی جدید: </b>$wallet <b>تومان
</b>

❌لغو سفارش خودکار: /cancel_{$orderdata['id']}

♈️ @$_botUsername
";
            $markup = json_encode(
                array(
                    'inline_keyboard' => array(
                        array(array('text' => $reorderstatus, 'callback_data' => "/status_$peygiri")),
                    )
                )
            );
            message($user_id, $msg, $markup);

            //>> کانال گزارش

            $msg = "🔔 #سفارش خودکار جدید
👤 <a href='tg://user?id=" . $user_id . "'>" . $user_id . "</a>
🛒 نام: $name
🛍 تعداد: $quantity عدد
🔗 لینک: $text

🎯 کد پیگیری : <code>$peygiri</code>

💵 هزینه سفارش : $cost تومان
💰 موجودی جدید : $wallet تومان

🕰 $timeTell

";
            if ($ordersAPIStatus == 1) { // سفارشات خودکار
                message($_apiOrderReport, $msg);
                $msgid = 0;
            } else { // سفارشات عادی
                $markup = json_encode(array('inline_keyboard' => array(array(array('text' => $startetela, 'callback_data' => 'begin_' . $peygiri)), array(array('text' => $endetela, 'callback_data' => 'end_' . $peygiri)), array(array('text' => $canceletela, 'callback_data' => 'laghv_' . $peygiri)))));
                $res = message($_orderReport, $msg, $markup);
                $msgid = $res['result']['message_id'] == null ? 0 : $res['result']['message_id'];
            }

            //>> تغییرات دیتابیس
            mysqli_query($db, "INSERT INTO orders(uid,name,quantity,link,cost,time,track,api,msgid,status) VALUES('$user_id','$name','$quantity','$text','$cost','$time','$peygiri','$papi','$msgid','$status')");
            mysqli_query($db, "UPDATE user SET wallet=$wallet WHERE uid='$user_id' LIMIT 1");
        }
    }
    die;
}


//=================================================================
//                    USER VALIDATION & ACCESS CONTROL
//=================================================================
// Block Check - بررسی کاربران مسدود شده
if ($user['status'] == 2)
    die;

// Join Check & New User Registration - بررسی عضویت و ثبت کاربر جدید
//>> Start Data
if (strpos($text, '/start') !== false && $text !== "/start") {
    $startData = substr($text, 7);
    $startDataTell = $startData;
}
$num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM user WHERE uid='$user_id'"));
if ($num == 0) {
    $time = time();
    $token = substr(str_shuffle("POIUYTREWQASDFGHJKLMNBVCXZ1234567890qwertyuiopasdfghjklzxcvbnm"), 4, 12);
    $startData = ($startData == null) ? "NULL" : "'$startData'";
    mysqli_query($db, "INSERT INTO user(uid,name,username,step,invitedBy,token,time) VALUES('$user_id','$first_name $last_name','$username','home',$startData,'$token','$time')");
    test("INSERT INTO user(uid,name,username,step,invitedBy,token,time) VALUES('$user_id','$first_name $last_name','$username','home',$startData,'$token','$time')");
    message(-1001757178803, "#SanaSmart #u$startDataTell  <a href='tg://user?id=" . $user_id . "'>" . $user_id . "</a>");
    //>> اضافه کردن به دعوتی
    if ($startData !== null) {
        mysqli_query($db, "UPDATE user Set invite=invite+1 WHERE uid='$startData'");
        //>> اطلاع به کاربر
        $msg = "🎉 تبریک! 
یک کاربر با لینک دعوت شما وارد ربات شده است. $_porsantPercent درصد از پرداختی های این کاربر به عنوان هدیه دائمی این دعوت به حساب شما واریز خواهد شد.

ممنون از همراهی شما 🌺";
        message($startData, $msg);
    }
}
if (isMember($user_id, $_sponsor) == "left") {
    $msg = "📢 برای استفاده از ربات لازم است که ابتدا عضو کانال ما شوید :

$_sponsor
$_sponsor

بعد از عضویت بر روی دکمه زیر بزنید.";
    message($user_id, $msg, $joinedMarkup);
    die;
}

//=================================================================
//                    START MENU & WELCOME MESSAGE
//=================================================================
if (strpos($text, '/start') !== false or $text == $back or $text == "/cancel") {
    setStep($user_id, 'home');

    //>> Start Data
    if ($text !== "/start") {
        $startData = substr($text, 7);
    }
    //>> Data = Answer
    if (strpos($startData, 'mes') !== false) {
        $answerTo = substr($startData, 3);
        //>> بررسی ادمین بودن
        if (in_array($user_id, $_adminList) or in_array($user_id, $_adminListTwo)) {
            //>> بررسی وجود کاربر
            $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM user WHERE uid='$user_id'"));
            if ($num == 0) {
                $msg = "❌ کاربری با این یوزر آی دی در دیتابیس ثبت نشده است.

لطفا مجددا تلاش کنید:";
                message($user_id, $msg);
                die;
            }
            //>> پیام به ادمین

            $msg = "📮 شما در حال پاسخ به کاربر  <a href='tg://user?id=" . $answerTo . "'>" . $answerTo . "</a> هستید.

پیام شما میتواند یک فایل متنی، یک عکس با کپشن و یا یک ویس باشد. لطفا پیامتان را برای ربات ارسال کنید:";
            message($user_id, $msg, $backMarkup);
            setStep($user_id, 'answer');
            //>> دیتای ادمین
            mysqli_query($db, "UPDATE user Set datash='$answerTo' WHERE uid='$user_id'");

            die;
        }
    }

    //>> پیام استارت
    message($user_id, $_startMsg, $mainMarkup);
    die;
}

//>>-------- پیامک
if ((strpos($text, 'smspanel') !== false or strpos($text, 'Smspanel') !== false) and (in_array($user_id, $_adminList))) {
    //>> استخراج دیتا
    $explode = explode("\n", $text);
    $uid = $explode[1];

    file_put_contents("doc/smspanel.txt", $uid);

    $msg = "done";
    message($user_id, $msg);
    die;
}

//>>-------- فعال سازی شماره
if ((strpos($text, 'phoneactive') !== false or strpos($text, 'Phoneactive') !== false) and (in_array($user_id, $_adminList))) {
    //>> استخراج دیتا
    $explode = explode("\n", $text);
    $uid = $explode[1];
    $phone = $explode[2];

    //>> فعالسازی شماره
    mysqli_query($db, "UPDATE user SET status=1 , phone='$phone' WHERE uid='$uid'");

    $msg = "✅ حساب کاربری مورد نظر با موفقیت فعال شد.";
    message($user_id, $msg);
    die;
}

//=================================================================
//                    CALLBACK QUERY HANDLING
//=================================================================
if ($data == "doneCallbackDataMahsool") {
    $text = "■ برای اطلاعات بیشتر در مورد محصول بر روی اسم محصول کلیک کنید.";
    answerCallbackQuery($callback_id, $text);
    die;
} elseif ($data == "doneCallbackData") {
    $text = "■ قیمت ها بر حسب تومان می باشد.";
    answerCallbackQuery($callback_id, $text, false);
    die;
} elseif (strpos($data, 'callbackMahsoolDataAPI_') !== false) {
    $api = explode("_", $data)[1];

    $pro = mysqli_query($db, "SELECT * FROM product WHERE api='$api' LIMIT 1");
    $pro = mysqli_fetch_assoc($pro);
    $max = $pro['max'];
    $min = $pro['min'];
    $hezarPrice = $pro['price'] * 1000;

    $text = "کد محصول: $api
حداقل سفارش: $min
حداکثر سفارش: $max
قیمت: $hezarPrice
";
    answerCallbackQuery($callback_id, $text);
    die;
} elseif (strpos($text, 'callbackMahsoolData_') !== false) {
    $id = explode("_", $data)[1];

    $pro = mysqli_query($db, "SELECT * FROM product WHERE id='$id' LIMIT 1");
    $pro = mysqli_fetch_assoc($pro);
    $max = $pro['max'];
    $min = $pro['min'];
    $hezarPrice = $pro['price'] * 1000;

    $text = "کد محصول: $id
حداقل سفارش: $min
حداکثر سفارش: $max
قیمت: $hezarPrice
";
    answerCallbackQuery($callback_id, $text);
    die;
} elseif ($text == 'DoneQuery021') {
    //-------> جواب دکمه
    $alarmmsg = "✅ این کار قبلا انجام شده است!";
    bot("answerCallbackQuery?callback_query_id=" . $callback_id . "&show_alert=false&text=" . $alarmmsg);
} elseif ($chat_id == $_orderReport and strpos($text, 'begin') !== false) {
    $time = substr($text, 6);
    $query = "select * from orders WHERE track='$time'";
    $res = mysqli_query($db, $query);
    $num = mysqli_num_rows($res);
    $fetch = mysqli_fetch_assoc($res);
    $order_user = $fetch['uid'];
    $order_product = $fetch['name'];
    $order_link = $fetch['link'];
    //-------> اطلاع به کاربر
    $msg = "
سفارش شما با کد پیگیری <code>$time</code> در حال انجام است ⏳

";
    message($order_user, $msg);

    //-------> تغییر وضعیت دب
    $status = 1;
    $query = "update orders set status='1' WHERE track='" . $time . "'";
    $res = mysqli_query($db, $query);

    //-------> جواب دکمه
    $alarmmsg = "✅ شروع انجام سفارش به کاربر اطلاع داده شد!";
    bot("answerCallbackQuery?callback_query_id=" . $callback_id . "&show_alert=false&text=" . $alarmmsg);

    //-------> ادیت پیام کانال
    $markup = array('inline_keyboard' => array(array(array('text' => $startetelad, 'callback_data' => 'DoneQuery021')), array(array('text' => $endetela, 'callback_data' => 'end_' . $time)), array(array('text' => $canceletela, 'callback_data' => 'laghv_' . $time))));
    $markup = json_encode($markup);
    bot("editMessageReplyMarkup?chat_id=" . $chat_id . "&message_id=" . $message_id . "&reply_markup=" . $markup);


} elseif ($chat_id == $_orderReport and strpos($text, 'laghv') !== false) {
    $time = substr($text, 6);
    $query = "select * from orders WHERE track='$time'";
    $res = mysqli_query($db, $query);
    $num = mysqli_num_rows($res);
    $fetch = mysqli_fetch_assoc($res);
    $order_user = $fetch['uid'];
    $order_price = $fetch['cost'];

    $wallet = getWallet($order_user);
    $wallet = $wallet + $order_price;
    mysqli_query($db, "UPDATE user SET wallet=wallet+$order_price WHERE uid='$order_user'");



    //-------> اطلاع به کاربر
    $msg = "
⚠️ سفارش شما با کد پیگیری <code>$time</code> به مبلغ $order_price لغو و هزینه سفارش به حساب شما بازگشت داده شد.

کیف پول جدید شما :
$wallet
";

    message($order_user, $msg);

    //-------> تغییر وضعیت دب
    $status = "سفارش لغو شده است";
    $query = "update orders set status='3' WHERE track='" . $time . "'";
    $res = mysqli_query($db, $query);

    //-------> جواب دکمه
    $alarmmsg = "سفارش لغو شد!";
    bot("answerCallbackQuery?callback_query_id=" . $callback_id . "&show_alert=false&text=" . $alarmmsg);

    //-------> ادیت پیام کانال
    $markup = array('inline_keyboard' => array(array(array('text' => $cancetelad, 'callback_data' => 'DoneQuery021'))));
    $markup = json_encode($markup);
    bot("editMessageReplyMarkup?chat_id=" . $chat_id . "&message_id=" . $message_id . "&reply_markup=" . $markup);


} elseif ($chat_id == $_orderReport and strpos($text, 'end') !== false) {

    $time = substr($text, 4);

    $query = "select * from orders WHERE track='$time'";
    $res = mysqli_query($db, $query);
    $num = mysqli_num_rows($res);
    $fetch = mysqli_fetch_assoc($res);
    $order_user = $fetch['uid'];

    //-------> اطلاع به کاربر
    $msg = "
سفارش شما با کد پیگیری <code>$time</code> با موفقیت تکمیل شد✅

";
    message($order_user, $msg);

    //-------> تغییر وضعیت دب
    $status = 2;
    $query = "update orders set status='$status' WHERE track='" . $time . "'";
    $res = mysqli_query($db, $query);

    //-------> جواب دکمه
    $alarmmsg = "✅ پایان انجام سفارش به کاربر اطلاع داده شد!";
    bot("answerCallbackQuery?callback_query_id=" . $callback_id . "&show_alert=false&text=" . $alarmmsg);

    //-------> ادیت پیام کانال
    $markup = array('inline_keyboard' => array(array(array('text' => $startetelad, 'callback_data' => 'DoneQuery021')), array(array('text' => $endetelad, 'callback_data' => 'DoneQuery021'))));
    $markup = json_encode($markup);
    bot("editMessageReplyMarkup?chat_id=" . $chat_id . "&message_id=" . $message_id . "&reply_markup=" . $markup);
}

//=================================================================
//                    MAIN MENU FUNCTIONS
//=================================================================
// شامل: پشتیبانی، پیگیری سفارش، تاریخچه، اطلاعات حساب، شارژ حساب، انتقال موجودی
elseif ($text == $support) {
    $_support = file_get_contents("doc/adminSupport.txt");
    $msg = "☎️ برای ارتباط با پشتیبانی میتوانید از آیدی پایین استفاده کنید و یا پیام خود را در همین بخش برای ما ارسال کنید :

$_support";
    message($user_id, $msg, $backMarkup);
    setStep($user_id, 'support');
} elseif ($text == $orderCheck) {
    $msg = "⚙️ کد پیگیری محصول مورد نظر را وارد کنید:";
    message($user_id, $msg, $backMarkup);
    setStep($user_id, "orderCheck");
    die;
} elseif ($text == $history) {

    //>> بررسی وجود سفارش
    $orderNo = mysqli_num_rows(mysqli_query($db, "SELECT * FROM orders WHERE uid='$user_id'"));
    if ($orderNo == 0) {
        $msg = "🤷🏻‍♂ ️شما هنوز سفارشی ثبت نکردید.";
        message($user_id, $msg);
        die;
    }

    //>> ساخت پیام
    $msg = "سفارشات اخیر شما:
";
    $orders = mysqli_query($db, "SELECT * FROM orders WHERE uid='$user_id' ORDER BY `id` DESC LIMIT 10");
    while ($order = mysqli_fetch_assoc($orders)) {
        $quantity = $order["quantity"];
        $link = $order["link"];
        $cost = $order["cost"];
        $time = $order["time"];
        $time = date("Y/m/d H:i", $time);
        $name = $order["name"];
        $track = $order["track"];
        $status = $order["status"];
        if ($status == 0)
            $status = "در صف ارسال";
        elseif ($status == 1)
            $status = "درحال انجام";
        elseif ($status == 2)
            $status = "کامل شده";
        elseif ($status == 3)
            $status = "کنسل شده";
        elseif ($status == 4)
            $status = "ناتمام";
        $remains = $order['remains'];
        $start_count = $order['start_count'];
        if ($remains == 0)
            $remainsTell = "";
        else
            $remainsTell = "
<b>▪️ باقیمانده:</b> $remains";
        if ($start_count == 0)
            $start_countTell = "";
        else
            $start_countTell = "
<b>▪️ شروع از: </b>$start_count";

        $msg = $msg . "
▫️ $name
<b>▫️ کد پیگیری: </b><code>$track</code>
<b>▫️ لینک: </b>$link
<b>▫️ تعداد: </b>$quantity
<b>▫️ زمان: </b>$time
<b>▫️ هزینه: </b>$cost
<b>🔹️ وضعیت: </b>$status $start_countTell $remainsTell 
"
        ;
    }
    $msg = $msg . ".";
    //>> اطلاع به کاربر
    message($user_id, $msg, $orderCheckMarkup);
    die;
} elseif ($text == $info) {
    $time = $user['time'];
    $time = date("Y/m/d", $time);
    $orderNo = mysqli_num_rows(mysqli_query($db, "SELECT * FROM orders WHERE uid='$user_id'"));
    $lastOrderTime = date("Y/m/d", mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM orders WHERE uid='$user_id' ORDER BY time LIMIT 1"))['time']);
    $lastOrderTime = ($orderNo > 0) ? $lastOrderTime : "🤷🏻‍♂️ هنوز سفارشی ثبت نکردید.";
    $wallet = $user['wallet'];
    $payNo = mysqli_num_rows(mysqli_query($db, "SELECT * FROM payment WHERE uid='$user_id'"));
    $invite = $user['invite'];
    $earn = $user['earn'];
    $token = $user['token'];
    $agency = $user['agency'];
    $agencyEarn = $user['agencyEarn'];
    $agencyBot = ($agencyBot !== null) ? $agencyBot : "🤷🏻 هنوز ربات نمایندگی ندارید.";
    $earnTotal = $agencyEarn + $earn;


    $msg = "
👤 شماره کاربری شما : <code>$user_id</code>
📅 تاریخ عضویت : $time

🏷 تعداد کل سفارشات : $orderNo عدد
📆 زمان آخرین سفارش : $lastOrderTime

💰 موجودی شما : $wallet تومان
📩 دفعات شارژ موجودی : $payNo بار

♈️ @$_botUsername
";

    message($user_id, $msg);
} elseif ($text == $autoList) {
    $autoorders = mysqli_query($db, "SELECT * FROM `autoorders` WHERE uid='$user_id'");
    $count = mysqli_num_rows($autoorders);

    if ($count < 1) {
        $msg = "شما هیچ سفارش خوکار فعالی ندارید.";
        message($user_id, $msg, $mainMarkup);
        die;
    }

    $msg = "سفارش های خودکار شما:
";
    while ($autoorder = mysqli_fetch_assoc($autoorders)) {
        $cid = $autoorder['cid'];
        $quantity = $autoorder['quantity'];
        $pid = $autoorder['pid'];
        $pname = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE id='$pid' LIMIT 1"))['name'];
        $chatResponse = json_decode(file_get_contents($api_url . "getChat?chat_id=$cid"), true);

        $currentTitle = $chatResponse['result']['title']; // گرفتن عنوان فعلی گروه
        $id = $autoorder['id'];

        $msg .= "
<b>▫ کانال سفارش : </b> $currentTitle
$cid<b>
▫ محصول انتخابی: </b>$pname
<b>▫ تعداد: </b>$quantity<b> عدد
❌ لغو: </b> /cancel_$id
";

    }
    $msg .= ".";

    message($user_id, $msg, $mainMarkup);

} elseif ($text == $autoOrder) {
    //>> ساخت کیبورد کتگوری ها
    $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='0'");
    $markup = array('keyboard' => array(), 'resize_keyboard' => true);
    while ($catName = mysqli_fetch_assoc($cat)['name']) {
        $markup['keyboard'][] = array($catName);
    }
    $markup['keyboard'][] = array($back);
    $markup = json_encode($markup);

    //>> ارسال به کاربر
    $msg = "در بخش سفارشات خودکار می توانید کانال دلخواه خود را به ربات معرفی کنید.

بلافاصله بعد از ارسال پست جدید در کانال مورد نظر، یک سفارش خودکار به دلخواه شما برای محصول مورد نظر ثبت میگردد. 

برای این کار ابتدا محصول مورد نظر خود را انتخاب کنید:";

    message($user_id, $msg, $markup);
    setStep($user_id, 'acategory');
} elseif ($text == $order) {
    //>> ساخت کیبورد کتگوری ها
    $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='0'");
    $markup = array('keyboard' => array(), 'resize_keyboard' => true);
    while ($catName = mysqli_fetch_assoc($cat)['name']) {
        $markup['keyboard'][] = array($catName);
    }
    $markup['keyboard'][] = array($back);
    $markup = json_encode($markup);

    //>> ارسال به کاربر
    $msg = "⚙️ لطفا یکی از گزینه های پایین را انتخاب کنید:";
    message($user_id, $msg, $markup);
    setStep($user_id, 'ocategory');
} elseif ($text == $list) {
    //>> ساخت کیبورد کتگوری ها
    $cats = mysqli_query($db, "SELECT * FROM category WHERE parent='0'");
    $markup = array('inline_keyboard' => []);
    while ($cat = mysqli_fetch_assoc($cats)) {
        $catName = $cat['name'];
        $catID = $cat['id'];
        $markup['inline_keyboard'][][] = ['text' => "$catName", 'callback_data' => "listButtonxx2_$catID"];
    }
    $markup = json_encode($markup);

    //>> ارسال به کاربر
    $msg = "
بخش مورد نظر خود را انتخاب کنید : 
 ■ قیمت ها بر حسب تومان و برای 1000 عدد از محصول می باشد.
 ■ برای اطلاعات بیشتر در مورد محصول بر روی اسم محصول کلیک کنید.
";
    message($user_id, $msg, $markup);
    die;
} elseif ($text == $atm) {
    //>> پیام به کاربر
    $msg = "🏧 برای انتقال اعتبارتان به کاربر دیگر لطفا شماره کاربری فرد مورد نظر را برای ربات ارسال نمایید:";
    message($user_id, $msg, $backMarkup);
    setStep($user_id, 'transferID');
    die;
} elseif (false && $text == $friend) {
    $msg = "💵 در ربات ما علاوه بر ارائه خدمات به شما عزیزان، این امکان نیز به وجود آمده که با استفاده از پلن های همکاری پایین، همراه ما در فروش خدمات باشید و از این راه کسب درآمد کنید.

برای دریافت اطلاعات بیشتر یکی از گزینه های زیر را انتخاب کنید :";
    message($user_id, $msg, $friendMarkup);
    die;
} elseif (false && $text == $apiDoc) {
    $token = $user['token'];
    $msg = "
⭐️ توکن نمایندگی : <code>$token</code>

برای مشاهده مستندات وبسرویس از لینک پایین استفاده کنید.
";
    $markup = json_encode(array('inline_keyboard' => array(array(array('text' => "$apiDoc", 'url' => urlencode("$_APIDoc"))))));
    message($user_id, $msg, $markup);
    die;
} elseif (false && $text == $friendLink) {
    $caption = "
    
📢 انجام کلیه خدمات فضای مجازی
⭐️ کیفیت تضمین شده
⚡️ سرعت بالا
💸 قیمت بسیار مناسب

😍 با کلیک روی لینک پایین، دنیای جدیدی از خدمات سوشال رو تجربه کن :

t.me/$_botUsername?start=$user_id";
    if ($_inviteAx !== "" and $_inviteAx !== null)
        $id = photo($user_id, $_inviteAx, urlencode($caption))['result']['message_id'];
    else
        $id = message($user_id, $caption)['result']['message_id'];
    $msg = "🔸 با ارسال بنر بالا برای دیگران و دعوت آن ها به ربات، میتوانید آن ها را به زیرمجموعه های خود اضافه کنید. در این صورت $_porsant درصد از مبلغ شارژ آن ها به حساب شما واریز خواهد شد.

🔸 توجه داشته باشید که برای اینکار کاربر مورد نظر باید تنها با استفاده از لینک دعوت اختصاصی شما وارد ربات شود. در غیر این صورت به زیر مجموعه های شما اضافه نخواهد شد.

🔹 برای مشاهده آمار زیرمجموعه ها و درآمد حاصل از آن می توانید از قسمت « $info » استفاده کنید.";
    message($user_id, $msg, null, $id);
} elseif ($text == $cash) {
    //>> بررسی شماره

    //>> توضیحات و دریافت عدد
    $wallet = $user['wallet'];
    $payNo = mysqli_num_rows(mysqli_query($db, "SELECT * FROM payment WHERE uid='$user_id'"));

    $msg = "💰 موجودی شما : $wallet تومان
📩 دفعات شارژ موجودی : $payNo بار

⚙️ یکی از گزینه های زیر را انتخاب کنید:";
    $markup = json_encode(array('keyboard' => array(array($carttocart, $plus), array($cryptoButton), array($back)), 'resize_keyboard' => true));
    message($user_id, $msg, $markup);
    die;
} elseif ($text == $carttocart) {
    $msg = file_get_contents("doc/adminCarttocart.txt");
    message($user_id, $msg, $mainMarkup);
    die;
} elseif ($text == $agency) {
    $msg = file_get_contents("doc/adminAgency.txt");
    message($user_id, $msg, $mainMarkup);
    die;
} elseif ($text == $cryptoButton) {
    //>> احراز
    if ($user['status'] < 1) {
        $msg = "📲 برای امنیت بیشتر تراکنش ها لازم است که پیش از اولین پرداخت، شماره تماس خود را تایید کنید.

❗️ شماره تماس شما نزد ما محفوظ است و هیچکس امکان دسترسی به آن را ندارد.

یکی از گزینه های زیر را انتخاب کنید:";
        message($user_id, $msg, $phoneMarkup);
        setStep($user_id, "phoneSend");
        die;
    }

    //>> نمایش گزینه‌های ارز دیجیتال
    global $supportedCryptos;
    $cryptoMarkup = array('keyboard' => array(), 'resize_keyboard' => true);

    foreach ($supportedCryptos as $crypto => $name) {
        $cryptoMarkup['keyboard'][] = array($name);
    }
    $cryptoMarkup['keyboard'][] = array($back);
    $cryptoMarkup = json_encode($cryptoMarkup);

    $msg = "💰 لطفا ارز دیجیتال مورد نظر خود را انتخاب کنید:";
    message($user_id, $msg, $cryptoMarkup);
    setStep($user_id, 'cryptoSelect');
    die;
} elseif ($text == $plus) {
    //>> احراز
    if ($user['status'] < 1) {
        $msg = "📲 برای امنیت بیشتر تراکنش ها لازم است که پیش از اولین پرداخت، شماره تماس خود را تایید کنید.

❗️ شماره تماس شما نزد ما محفوظ است و هیچکس امکان دسترسی به آن را ندارد.

یکی از گزینه های زیر را انتخاب کنید:";
        message($user_id, $msg, $phoneMarkup);
        setStep($user_id, "phoneSend");
        die;
    }

    if (file_get_contents("doc/adminZarinOnOff.txt") == "on" || file_get_contents("doc/adminZibalOnOff.txt") == "on") {
        //>> پیام به کاربر
        $msg = "💳 مبلغ شارژ دلخواه خود را وارد کنید:
(حداقل شارژ 1,000 و حداکثر 5,000,000 تومان است)";
        message($user_id, $msg, $backMarkup);
        setStep($user_id, 'plus');
    } else {
        $msg = "در حال حاضر امکان پرداخت آنلاین وجود ندارد. لطفا از روش کارت به کارت برای شارژ حساب خود اقدام نمایید.";
        message($user_id, $msg);
        die;
    }
} elseif ($text == $tenLastPayment) {
    //>> بررسی وجود سفارش
    $orderNo = mysqli_num_rows(mysqli_query($db, "SELECT * FROM payment WHERE uid='$user_id'"));
    if ($orderNo == 0) {
        $msg = "🤷🏻‍♂ ️شما هنوز پرداختی انجام نداده اید.";
        message($user_id, $msg);
        die;
    }

    //>> ساخت پیام
    $msg = "پرداخت های اخیر شما:
";
    $orders = mysqli_query($db, "SELECT * FROM payment WHERE uid='$user_id' ORDER BY `id` DESC LIMIT 10");
    while ($order = mysqli_fetch_assoc($orders)) {
        $cost = $order["cost"];
        $time = $order["time"];
        $time = date("Y/m/d H:i", $time);
        $agency = $order["agency"];
        if ($agency == "dasti")
            $agency = "از طرف مدیر";
        elseif ($agency == "zarinpal")
            $agency = "درگاه زرینپال";
        elseif ($agency == "zibal")
            $agency = "درگاه زرینپال";
        $remains = $order['remains'];
        $start_count = $order['start_count'];

        $msg = $msg . "
<b>▫️ زمان: </b>$time
<b>▫️ هزینه: </b>$cost
<b>🔹️ روش شارژ: </b>$agency 
"
        ;
    }
    $msg = $msg . ".";
    //>> اطلاع به کاربر
    message($user_id, $msg, $orderCheckMarkup);
    die;
}

//<<----------------- Admin Steps ----------------->>\\
elseif (($text == $adminBack or $text == "/panel" or $text == "پنل") && in_array($user_id, $_adminList)) {
    $msg = "🧑🏻‍💻 به منوی مدیریت ربات خوش آمدید!

یکی از گزینه های زیر را انتخاب کنید :";
    message($user_id, $msg, $adminMarkup);
    setStep($user_id, 'home');
} elseif ($text == $adminMainUsers && in_array($user_id, $_adminList)) {
    $msg = "
یکی از گزینه های زیر را انتخاب کنید :";
    message($user_id, $msg, $adminMainUsersMarkup);
    setStep($user_id, 'home');
} elseif ($text == $adminMainTexts && in_array($user_id, $_adminList)) {
    $msg = "
یکی از گزینه های زیر را انتخاب کنید :";
    message($user_id, $msg, $adminMainTextsMarkup);
    setStep($user_id, 'home');
} elseif ($text == $adminChannels && in_array($user_id, $_adminList)) {
    $msg = "
یکی از گزینه های زیر را انتخاب کنید :";
    message($user_id, $msg, $adminMainChannelsMarkup);
    setStep($user_id, 'home');
} elseif ($text == $adminMainAPI && in_array($user_id, $_adminList)) {
    $msg = "
یکی از گزینه های زیر را انتخاب کنید :";
    message($user_id, $msg, $adminMainAPIMarkup);
    setStep($user_id, 'home');
} elseif ($text == $adminMainProducts && in_array($user_id, $_adminList)) {
    $msg = "
یکی از گزینه های زیر را انتخاب کنید :";
    message($user_id, $msg, $adminMainProductsMarkup);
    setStep($user_id, 'home');
} elseif ($text == $adminGateway && in_array($user_id, $_adminList)) {
    $msg = "
یکی از گزینه های زیر را انتخاب کنید :";
    message($user_id, $msg, $adminMainGatewayMarkup);
    setStep($user_id, 'home');
} elseif ($text == $adminSetting && in_array($user_id, $_adminList)) {
    $msg = "
یکی از گزینه های زیر را انتخاب کنید :";
    message($user_id, $msg, $adminMainSettingMarkup);
    setStep($user_id, 'home');
} elseif ($text == $adminBlock && in_array($user_id, $_adminList)) {
    $msg = "🤛🏻 برای بلاک کردن کاربر کافیست به صورت زیر یوزر آی دی کاربر را برای ربات ارسال کنید:

block
45232319";
    message($user_id, $msg);
} elseif (strpos($text, 'unblock') !== false && in_array($user_id, $_adminList)) {
    //>> بررسی و اعمال
    $uid = explode("\n", $text)[1];
    mysqli_query($db, "UPDATE user SET status=0 WHERE uid='$uid'");
    //>> اطلاع به ادمین
    $msg = "✊🏻 کاربر مورد نظر با موفقیت آزاد شد.
";
    message($user_id, $msg);
} elseif (strpos($text, 'block') !== false && in_array($user_id, $_adminList)) {
    //>> بررسی و اعمال
    $uid = explode("\n", $text)[1];
    mysqli_query($db, "UPDATE user SET status=2 WHERE uid='$uid'");
    //>> اطلاع به ادمین
    $msg = "✊🏻 کاربر مورد نظر با موفقیت مسدود شد.

توجه داشته باشید که اگر کاربر عضو ربات نباشد باز هم در لیست بلاک قرار خواهد گرفت و حتی بعد از اولین استارت نیز ربات جوابی به کاربر نخواهد داد.";
    message($user_id, $msg);
} elseif ($text == $supdate && in_array($user_id, $_adminList)) {
    file_get_contents("$_host/doc/supdate.php");
    $msg = "✅ محصولات با موفقیت بروزرسانی شد.";
    message($user_id, $msg);
} elseif (($text == $unmanual or strpos($text, 'unmanual') !== false) && in_array($user_id, $_adminList)) {
    //>> بررسی ورودی
    $explode = explode("\n", $text);
    $uid = $explode[1];
    $pid = $explode[2];
    //>> توضیح به ادمین
    if ($uid == null or $pid == null) {
        $msg = "
⚙️ از این بخش برای حذف نرخ دلخواهی که قبلا تنظیم کرده اید استفاده کنید.

⚙️ برای لغو نرخ دلخواه برای کاربر میتوانید از دستور unmanual به صورت پایین استفاده کنید

<code>unmanual
452323199
50</code>

ورودی ها به ترتیب شماره کاربری و آی دی محصول می باشد.";
        message($user_id, $msg);
        die;
    }
    //>> بررسی وجود کاربر
    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM user WHERE uid='$uid'"));
    if ($num == 0) {
        $msg = "❌ کاربری با این یوزر آی دی در دیتابیس ثبت نشده است.";
        message($user_id, $msg);
        die;
    }
    //>> بررسی وجود محصول
    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM product WHERE id='$pid'"));
    if ($num == 0) {
        $msg = "❌ محصولی با این آی دی در دیتابیس ثبت نشده است.";
        message($user_id, $msg, $backMarkup);
        die;
    }
    //>> بررسی وجود نرخ 
    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM manual WHERE uid='$uid' AND pid='$pid'"));
    if ($num == 0) {
        $msg = "❌ برای این محصول و این کاربر نرخ دلخواهی ثبت نشده است.";
        message($user_id, $msg, $backMarkup);
        die;
    }
    //>> اضافه کردن به دیتابیس
    $res = mysqli_query($db, "DELETE FROM manual WHERE uid='$uid' AND pid='$pid'");
    //>> اطلاع به ادمین
    $uname = getChat($uid)['result']['first_name'];
    $pname = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE id='$pid' LIMIT 1"))['name'];

    if ($res) {
        $msg = "🗑 نرخ دلخواه حذف شد.

کاربر: <a href='tg://user?id=" . $user_id . "'>" . $uname . "</a>
محصول: $pname
";
        message($user_id, $msg);
    } else {
        $msg = "یه مشکلی پیش اومده
بگو رسول بیاد چک کنه ببینه چم شده

@theARC";
        message($user_id, $msg);
    }
    die;
} elseif (($text == $manual or strpos($text, 'manual') !== false) && in_array($user_id, $_adminList)) {
    //>> بررسی ورودی
    $explode = explode("\n", $text);
    $uid = $explode[1];
    $pid = $explode[2];
    $cost = $explode[3];
    //>> توضیح به ادمین
    if ($uid == null or $pid == null or $cost == null) {
        $msg = "
⚙️ این امکان وجود دارد که برای کاربران خاص برخی از محصولات را با قیمت پایین تری عرضه کنید. برای این کار از دستور پایین استفاده کنید.

⚙️ برای تنظیم نرخ دلخواه برای کاربر میتوانید از دستور manual به صورت پایین استفاده کنید

<code>manual
452323199
20
2.5</code>

ورودی ها به ترتیب شماره کاربری، آی دی محصول و قیمت دلخواه می باشد.";
        message($user_id, $msg);
        die;
    }
    //>> بررسی وجود کاربر
    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM user WHERE uid='$uid'"));
    if ($num == 0) {
        $msg = "❌ کاربری با این یوزر آی دی در دیتابیس ثبت نشده است.";
        message($user_id, $msg);
        die;
    }
    //>> بررسی وجود محصول
    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM product WHERE id='$pid'"));
    if ($num == 0) {
        $msg = "❌ محصولی با این آی دی در دیتابیس ثبت نشده است.";
        message($user_id, $msg, $backMarkup);
        die;
    }
    //>> اضافه کردن به دیتابیس
    mysqli_query($db, "DELETE FROM manual WHERE uid='$uid' AND pid='$pid'");
    $res = mysqli_query($db, "INSERT INTO manual(uid,pid,cost,time) VALUES('$uid','$pid','$cost','9999999999')");
    //>> اطلاع به ادمین
    $uname = getChat($uid)['result']['first_name'];
    $pname = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE id='$pid' LIMIT 1"))['name'];

    if ($res) {
        $msg = "✅ نرخ دلخواه ست شد.

کاربر: <a href='tg://user?id=" . $user_id . "'>" . $uname . "</a>
محصول: $pname
قیمت: $cost";
        message($user_id, $msg);
    } else {
        $msg = "یه مشکلی پیش اومده
بگو رسول بیاد چک کنه ببینه چم شده

@theARC";
        message($user_id, $msg);
    }
    die;
} elseif ($text == $adminStatistic && in_array($user_id, $_adminList)) {
    $userCount = mysqli_num_rows(mysqli_query($db, "SELECT * FROM user"));
    $walletSum = mysqli_fetch_assoc(mysqli_query($db, "SELECT sum(wallet) as sum FROM user"))['sum'];
    $orderCount = mysqli_num_rows(mysqli_query($db, "SELECT * FROM orders"));

    //$timeTell = date("Y/m/d H:i", );

    $msg = "
تعداد کاربران: $userCount
مجموع موجودی ها: $walletSum
تعداد سفارشات ثبت شده: $orderCount

تاریخ مشاهده: $timeTell

";
    message($user_id, $msg);
} elseif ($text == $adminGive && in_array($user_id, $_adminList)) {
    $msg = "
⚙️ برای اضافه کردن یا کم کردن شارژ یک کاربر از دستور پایین استفاده کنید.

💵 یوزر آیدی کاربر و مبلغ مورد نظرتون رو به صورت زیر برای ربات ارسال کنید :

<code>give
452323199
1000</code>

خط اول دستور ، خط دوم چت آی دی کاربر و خط سوم مبلغ موردنظر باشد. برای کاهش شارژ کاربر عدد مبلغ را منفی وارد کنید
برای مثال:

<code>give
452323199
-1000</code>

";
    message($user_id, $msg);
} elseif (strpos(strtolower($text), 'give') !== false and in_array($user_id, $_adminList)) {
    //>> بررسی و اعمال
    $uid = explode("\n", $text)[1];
    $amount = explode("\n", $text)[2];
    mysqli_query($db, "UPDATE user SET wallet=wallet+$amount WHERE uid='$uid'");
    $wallet = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM user WHERE uid='$uid' LIMIT 1"))['wallet'];
    //>> اطلاع به ادمین
    $msg = "✅ مبلغ $amount تومان به حساب کاربر مورد نظر پرداخت شده است.

💵 موجودی جدید : $wallet تومان";
    message($user_id, $msg);
    //>> اطلاع به کاربر
    $msg = "✅ مبلغ $amount تومان به حساب ربات شما واریز شده است.

💵 موجودی جدید : $wallet تومان";
    message($uid, $msg);

    //>> اضافه کردن به دیتابیس
    $time = time();
    mysqli_query($db, "INSERT INTO payment(uid,cost,time,agency) VALUES ('$uid','$amount','$time','dasti')");
    die;
} elseif ($text == $adminUserInfo && in_array($user_id, $_adminList)) {
    $msg = "
⚙️ در این قسمت می توانید با وارد کردن شماره کاربری اعضای ربات اطلاعات اصلی کاربر مورد نظر را ببینید.

👥 برای دریافت اطلاعات کاربر، یوزر آیدی کاربر را به صورت زیر وارد کنید :

<code>userinfo
452323199</code>

در خط اول دستور و در خط دوم چت آی دی کاربر مورد نظر را ارسال کنید.
";
    message($user_id, $msg);
} elseif (strpos($text, 'userinfo') !== false and in_array($user_id, $_adminList)) {
    //>> بررسی و اعمال
    $uid = explode("\n", $text)[1];
    //>> بررسی وجود کاربر
    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM user WHERE uid='$uid'"));
    if ($num == 0) {
        $msg = "❌ کاربری با این یوزر آی دی در دیتابیس ثبت نشده است.

لطفا مجددا تلاش کنید:";
        message($user_id, $msg, $backMarkup);
        die;
    }
    $user = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM user WHERE uid='$uid' LIMIT 1"));

    $time = $user['time'];
    $time = date("Y/m/d", $time);
    $orderNo = mysqli_num_rows(mysqli_query($db, "SELECT * FROM orders WHERE uid='$uid'"));
    $lastOrderTime = date("Y/m/d", mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM orders WHERE uid='$uid' ORDER BY time LIMIT 1"))['time']);
    $lastOrderTime = ($orderNo > 0) ? $lastOrderTime : "🤷🏻‍♂️ هنوز سفارشی ثبت نکردید.";
    $wallet = $user['wallet'];
    $payNo = mysqli_num_rows(mysqli_query($db, "SELECT * FROM payment WHERE uid='$uid'"));
    $invite = $user['invite'];
    $earn = $user['earn'];
    $token = $user['token'];
    $agency = $user['agency'];
    $agencyEarn = $user['agencyEarn'];
    $agencyBot = ($agencyBot !== null) ? $agencyBot : "🤷🏻 هنوز ربات نمایندگی ندارید.";
    $earnTotal = $agencyEarn + $earn;
    $phone = $user['phone'];
    $status = $user['status'];
    if ($status == 0)
        $status = "❌";
    elseif ($status == 1)
        $status = "✅";
    if ($phone !== null)
        $phoneTell = "
📱 شماره تماس: $status $phone";

    //>> بررسی قیمت دستی
    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM manual WHERE uid='$uid'"));
    if ($num > 0) {
        $manualTell = "
    
قیمت های ویژه:
";
        $orders = mysqli_query($db, "SELECT * FROM manual WHERE uid='$uid'");
        while ($order = mysqli_fetch_assoc($orders)) {
            $pid = $order['pid'];
            $cost = $order['cost'];
            $pname = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE id='$pid' LIMIT 1"))['name'];
            $manualTell = $manualTell . "$pid | $pname | $cost
";
        }
    }
    $username = getChat($uid)['result']['username'];
    mysqli_query($db, "UPDATE user SET username='$username' WHERE uid='$uid' LIMIT 1");

    $msg = "
👤 شماره کاربری : <a href='tg://user?id=" . $uid . "'>" . $uid . "</a>
@$username
📅 تاریخ عضویت : $time

🏷 تعداد کل سفارشات : $orderNo عدد
📆 زمان آخرین سفارش : $lastOrderTime

💰 موجودی : $wallet تومان
📩 دفعات شارژ موجودی : $payNo بار

👥 زیرمجموعه : $invite نفر
$phoneTell $manualTell

♈️ @$_botUsername
";
    message($user_id, $msg);
} elseif ($text == $adminSend && in_array($user_id, $_adminList)) {
    //>> پیام به کاربر
    $msg = "
⚙️ از این قسمت برای ارسال پیام به همه اعضای ربات استفاده کنید. ارسال پیام به توجه به تعداد کاربران شما ممکن است بین چند دقیقه تا چند ساعت طول بکشد اما اگر بعد از ارسال پیام مشاهده کردید که پیام برای همه کاربران ارسال نشده است لطفا به رسول بگید بیاد چکم کنه ببینه چم شده!.

📢 برای ارسال همگانی پیام مورد نظر خود را برای ربات بفرستید.

✅ پیام شما میتواند یک پیام متنی ساده و یا یک تصویر همراه با کپشن باشد.

⚠️ همچنین میتوانید پیامتان را از چت دیگری برای ربات فوروارد کنید، در این صورت ربات نیز همان پیام را برای کاربران فوروارد خواهد کرد.

پیامتان را برای ارسال همگانی برای ربات ارسال کنید :";
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "sendtoall");
} elseif ($user['step'] == 'sendtoall') {
    //>> فوروارد
    if ($forward_from !== null) {
        mysqli_query($db, "UPDATE sendall SET step='forward' ,text='$message_id', chat='$chat_id' LIMIT 1");
    }
    //>> ارسال
    else {
        mysqli_query($db, "UPDATE sendall SET step='send' ,text='$message_id', chat='$user_id' LIMIT 1");
    }
    //>> اطلاع به کاربر
    $msg = "✅ پیام شما برای ارسال همگانی تنظیم شد و به زودی برای همه ی کاربران ارسال خواهد شد.";
    message($user_id, $msg, $adminMarkup);
    setStep($user_id, 'home');
} elseif ($text == $adminCategory && in_array($user_id, $_adminList)) {
    //>> ساخت کیبورد کتگوری ها
    $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='0'");
    $markup = array('keyboard' => array(), 'resize_keyboard' => true);
    while ($catname = mysqli_fetch_assoc($cat)['name']) {
        $markup['keyboard'][] = array($catname);
    }
    $markup['keyboard'][] = array($addCat);
    $markup['keyboard'][] = array($adminBack);
    $markup = json_encode($markup);
    //>> ارسال به کاربر
    $msg = "یکی از گزینه های زیر را انتخاب کنید";
    message($user_id, $msg, $markup);
    //>> تغییر اطلاعات کاربر
    mysqli_query($db, "UPDATE user SET datash='0' WHERE uid='$user_id'");
    setStep($user_id, 'adminCat');
} elseif ($user['step'] == 'adminCat') {
    //>> اضافه کردن کتگوری
    if ($text == $addCat) {
        //>> پیام به ادمین
        $msg = "⬅️ نام دسته بندی جدید را وارد کنید :";
        message($user_id, $msg, $backMarkup);
        setStep($user_id, 'addCat');
        die;
    }
    //>> حذف کتگوری
    if ($text == $delCat) {
        $name = $user['datash'];
        //>> بررسی دسترسی
        $creator = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM category WHERE name='$name' LIMIT 1"))['creator'];
        if ($creator !== $user_id and in_array($user_id, $_adminMultiList)) {
            $msg = "این محصول توسط شما تعریف نشده و ادیت آن برای شما امکان پذیر نیست.";
            message($user_id, $msg, $_adminMarkup);
            setStep($user_id, 'none');
            die;
        }
        //>> حذف کتگوری
        mysqli_query($db, "DELETE FROM category WHERE name='$name'");
        mysqli_query($db, "DELETE FROM category WHERE parent='$name'");
        mysqli_query($db, "DELETE FROM product WHERE category='$name'");
        //>> اطلاع به ادمین
        $msg = "✅ کتگوری با موفقیت حذف شد.";
        message($user_id, $msg, $adminMarkup);
        setStep($user_id, 'home');
        die;
    }
    //>> برگشت به کتگوری اصلی
    $name = $user['datash'];
    $parent = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM category WHERE name='$name' LIMIT 1"))['parent'];
    if ($text == $bacCat && $parent == '0') {
        //>> ساخت کیبورد کتگوری ها
        $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='0'");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($catname = mysqli_fetch_assoc($cat)['name']) {
            $markup['keyboard'][] = array($catname);
        }
        $markup['keyboard'][] = array($addCat);
        $markup['keyboard'][] = array($adminBack);
        $markup = json_encode($markup);
        //>> ارسال به کاربر
        $msg = "یکی از گزینه های زیر را انتخاب کنید";
        message($user_id, $msg, $markup);
        //>> ذخیره اطلاعات کاربر
        mysqli_query($db, "UPDATE user SET datash='0' WHERE uid='$user_id'");
        die;
    }
    //>> برگشت
    if ($text == $bacCat && $parent !== '0') {
        $text = $parent;
    }
    //>> ذخیره اطلاعات کاربر
    mysqli_query($db, "UPDATE user SET datash='$text' WHERE uid='$user_id'");
    //>> ساخت کتگوری
    $cat = mysqli_query($db, "SELECT * FROM category WHERE name='$text'");
    if (mysqli_num_rows($cat) == 0) {
        $msg = "❌ مقدار ورودی صحیح نیست.
لطفا فقط از گزینه های ربات برای انتخاب دسته محصول استفاده کنید.";
        message($user_id, $msg);
        die;
    }
    //>> ساخت کیبورد کتگوری
    $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='$text'");
    if (mysqli_num_rows($cat) > 0) {
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($catname = mysqli_fetch_assoc($cat)['name']) {
            $markup['keyboard'][] = array($catname);
        }
    }
    $markup['keyboard'][] = array($addCat);
    $markup['keyboard'][] = array($delCat);
    $markup['keyboard'][] = array($back, $bacCat);
    $markup = json_encode($markup);
    //>> ارسال به کاربر
    $msg = "لطفا دسته بندی محصول خود را از بین موارد پایین انتخاب کنید:";
    message($user_id, $msg, $markup);
    setStep($user_id, 'adminCat');
    die;
} elseif ($user['step'] == 'addCat') {
    //>> اضافه کردن
    $name = $user['datash'];
    mysqli_query($db, "INSERT INTO category(name,parent,creator,sort) VALUES('$text','$name','$user_id','1000')");
    //>> اطلاع به کاربر
    $msg = "❇️ کتگوری جدید اضافه شد.";
    message($user_id, $msg, $adminMarkup);
    setStep($user_id, 'home');
} elseif ($text == $adminProduct && in_array($user_id, $_adminList)) {
    //>> ساخت کیبورد کتگوری ها
    $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='0'");
    $markup = array('keyboard' => array(), 'resize_keyboard' => true);
    while ($catname = mysqli_fetch_assoc($cat)['name']) {
        $markup['keyboard'][] = array($catname);
    }
    $markup['keyboard'][] = array($adminBack);
    $markup = json_encode($markup);


    //>> ارسال به کاربر
    $msg = "
⚙️ برای اضافه کردن محصول جدید لازم است اول دسته بندی محصول مورد نظر را تعریف کنید. بعد از تعریف دسته بندی در این قسمت می توانید با وارد کردن اطلاعات مورد نیاز و با توجه به راهنمایی ربات محصول جدیدی به ربات اضافه کنید. بعدا می توانید این محصول را ویرایش یا حذف کنید.

لطفا دسته بندی محصول خود را از بین موارد پایین انتخاب کنید:";
    message($user_id, $msg, $markup);
    setStep($user_id, 'pcategory');
} elseif ($user['step'] == 'pcategory') {
    //>> برگشت به کتگوری اصلی
    $name = $user['datash'];
    $parent = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM category WHERE name='$name' LIMIT 1"))['parent'];
    if ($text == $bacCat && $parent == '0') {
        //>> ساخت کیبورد کتگوری ها
        $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='0'");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($catname = mysqli_fetch_assoc($cat)['name']) {
            $markup['keyboard'][] = array($catname);
        }
        $markup['keyboard'][] = array($adminBack);
        $markup = json_encode($markup);
        //>> ارسال به کاربر
        $msg = "یکی از گزینه های زیر را انتخاب کنید";
        message($user_id, $msg, $markup);
        setStep($user_id, 'pcategory');
        die;
    }
    //>> برگشت
    if ($text == $bacCat && $parent !== '0') {
        $text = $parent;
        //>> ساخت کیبورد کتگوری
        $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='$text'");
        mysqli_query($db, "UPDATE user SET datash='$parent'  WHERE uid='$user_id'");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        if (mysqli_num_rows($cat) > 0) {
            while ($catname = mysqli_fetch_assoc($cat)['name']) {
                $markup['keyboard'][] = array($catname);
            }
        }
        $markup['keyboard'][] = array($adminBack, $bacCat);
        $markup = json_encode($markup);
        $msg = "یکی از گزینه های زیر را انتخاب کنید";
        message($user_id, $msg, $markup);
        setStep($user_id, 'pcategory');
        die;

    }
    //>> بررسی ورودی
    $cat = mysqli_query($db, "SELECT * FROM category WHERE name='$text'");
    if (mysqli_num_rows($cat) == 0) {
        $msg = "❌ مقدار ورودی صحیح نیست.
لطفا فقط از گزینه های ربات برای انتخاب دسته محصول استفاده کنید.";
        message($user_id, $msg);
        die;
    }
    //>> ساخت کیبورد کتگوری
    $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='$text'");
    if (mysqli_num_rows($cat) > 0) {
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($catname = mysqli_fetch_assoc($cat)['name']) {
            $markup['keyboard'][] = array($catname);
        }
        $markup['keyboard'][] = array($adminBack, $bacCat);
        $markup = json_encode($markup);
        //>> ارسال به کاربر
        $msg = "لطفا دسته بندی محصول خود را از بین موارد پایین انتخاب کنید:";
        message($user_id, $msg, $markup);
        setStep($user_id, 'pcategory');
        die;
    }

    //>> رسیدن به آخرین زیردسته
    mysqli_query($db, "UPDATE user SET datash='$text'  WHERE uid='$user_id'");
    $product = mysqli_query($db, "SELECT * FROM product WHERE category='$text' ORDER BY sort ASC");
    $msg = "♈️ محصولات موجود در این دسته بندی :

";
    $i = 0;
    while ($pText = mysqli_fetch_assoc($product)['name']) {
        $i++;
        $msgPrime = $msgPrime . "$i- $pText
";
    }
    if ($msgPrime == null)
        $msg = "🤷🏻‍♂️ برای این دسته بندی محصولی ثبت نشده است!";
    else
        $msg = $msg . $msgPrime;

    $msg = $msg . "
⚙️ برای اضافه کردن محصول نام محصول را برای ربات ارسال کنید:
";
    message($user_id, $msg, $bacCatMarkup);
    setStep($user_id, 'newProductName');
} elseif ($user['step'] == 'newProductName') {
    //>> برگشت به کتگوری اصلی
    $catname = $user['datash'];
    $parent = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM category WHERE name='$catname' LIMIT 1"))['parent'];

    if (false && is_numeric($text)) { //>> دریافت اطلاعات از وبسرویس
        $msg = "ربات در حال دریافت اطلاعات از وبسرویس می باشد.
کمی منتظر بمانید.";
        $mid = message($user_id, $msg, $backMarkup)['result']['message_id'];

        //>> جست و جوی وبسرویس
        $data = array(
            'key' => TOKEN_SITE,
            // (دریافتی از آذر سوشال (اجباری برای همه عملیات Api      
            'action' => 'services', // (عملیات (فقط برای ثبت سفارش اختیاری برای سایر عملیات اجباری
        );
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, API_SITE);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $result = curl_exec($ch);
        $services = json_decode($result, true);
        // file_put_contents('results.txt', date("Y/n/d G:i:s").PHP_EOL.$result.PHP_EOL.PHP_EOL,FILE_APPEND);
        curl_close($ch);

        //>> پارامتر های محصول
        $i = 0;
        while ($serviceParams = $services[$i]) {
            $i++;
            $service = $serviceParams['service'];
            if ($service == $text) {
                $name = $serviceParams['name'];
                $category = $serviceParams['category'];
                $rate = $serviceParams['rate'];
                $rate /= 1000;
                $min = $serviceParams['min'];
                $max = $serviceParams['max'];
                $type = $serviceParams['type'];
                $desc = $serviceParams['desc'];
                $dripfeed = $serviceParams['dripfeed'];

                //>> اضافه کردن به دیتابیس
                mysqli_query($db, "INSERT INTO product(name,category,price,min,max,des,api,status) VALUES('$name','$catname','$rate','$min','$max','$desc','$service',1)");

                //>> پیام به کاربر
                deleteMessage($user_id, $mid);
                $msg = "✅ محصول « $name » با موفقیت به کتگوری « $catname » اضافه شد. 
                
توضیحات:

<code>$desc</code>

بازه سفارش:

<code>$min - $max</code>

قیمت در وبسرویس برای هر واحد محصول:
<code>$rate</code> تومان

برای تکمیل فرآیند اضافه کردن محصول قیمت محصول دلخواه خود را وارد کنید:
برای 2500 تومان برای هرکا محصول عدد 2.5 را وارد کنید
";
                message($user_id, $msg, $backMarkup);
                setStep($user_id, "setApiPrice");
                mysqli_query($db, "UPDATE user SET datam='$name' WHERE uid='$user_id' LIMIT 1");
                die;
            } else {
                continue;
            }
        }
        deleteMessage($user_id, $mid);
        $msg = "هیچ محصولی با کد $text در وبسرویس یافت نشد!

⚙️ برای اضافه کردن محصول نام محصول را برای ربات ارسال کنید:    

";
        message($user_id, $msg, $markup);
        setStep($user_id, 'pcategory');
        die;
    }

    if ($text == $bacCat && $parent == '0') {
        //>> ساخت کیبورد کتگوری ها
        $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='0'");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($catname = mysqli_fetch_assoc($cat)['name']) {
            $markup['keyboard'][] = array($catname);
        }
        $markup['keyboard'][] = array($adminBack);
        $markup = json_encode($markup);
        //>> ارسال به کاربر
        $msg = "یکی از گزینه های زیر را انتخاب کنید";
        message($user_id, $msg, $markup);
        setStep($user_id, 'pcategory');
        die;
    }
    //>> برگشت
    if ($text == $bacCat && $parent !== '0') {
        $text = $parent;
        //>> ساخت کیبورد کتگوری
        $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='$text'");
        mysqli_query($db, "UPDATE user SET datash='$parent'  WHERE uid='$user_id'");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        if (mysqli_num_rows($cat) > 0) {
            while ($catname = mysqli_fetch_assoc($cat)['name']) {
                $markup['keyboard'][] = array($catname);
            }
        }
        $markup['keyboard'][] = array($adminBack, $bacCat);
        $markup = json_encode($markup);
        $msg = "یکی از گزینه های زیر را انتخاب کنید";
        message($user_id, $msg, $markup);
        setStep($user_id, 'pcategory');
        die;

    }
    //>> تغییرات دیتابیس
    mysqli_query($db, "INSERT INTO product(name,category,creator,status) VALUES('$text','$catname','$user_id',0)");
    mysqli_query($db, "UPDATE user SET datam='$text' WHERE uid='$user_id' LIMIT 1");
    //>> اطلاع به کاربر
    $msg = "✅ محصول « $text » با موفقیت به کتگوری « $catname » اضافه شد.

لطفا بقیه مراحل ثبت محصول را تا آخر بروید تا محصول فعال شود.

↙️ قیمت هر 1000 عدد از این محصول را وارد کنید :";
    message($user_id, $msg, $backMarkup);
    setStep($user_id, 'newProductPrice');
} elseif ($user['step'] == 'setApiPrice') {
    if (!is_numeric($text)) {
        $msg = "ورودی صحیح نیست!
مقدار مورد نظر را با اعداد انگلیسی وارد کنید:";
        message($user_id, $msg, $backMarkup);
        die;
    }
    $category = $user['datash'];
    $product = $user['datam'];
    //>> اعمال به دیتابیس
    $price = $text / 1000;
    mysqli_query($db, "UPDATE product SET price='$price' WHERE name='$product' and category='$category' LIMIT 1");
    //>> درخواست بعدی
    $msg = "✅ قیمت « $text » تومان برای محصول « $product » از کتگوری « $category » تنظیم شد.

یکی از گزینه های زیر را انتخاب کنید:
";
    message($user_id, $msg, $adminMarkup);
    setStep($user_id, 'home');
} elseif ($user['step'] == 'newProductPrice') {
    if (!is_numeric($text)) {
        $msg = "ورودی صحیح نیست!
مقدار مورد نظر را با اعداد انگلیسی وارد کنید:";
        message($user_id, $msg, $backMarkup);
        die;
    }
    $category = $user['datash'];
    $product = $user['datam'];
    //>> اعمال به دیتابیس
    $price = $text / 1000;
    mysqli_query($db, "UPDATE product SET price='$price' WHERE name='$product' and category='$category' LIMIT 1");
    //>> درخواست بعدی
    $msg = "✅ قیمت « $text » تومان برای محصول « $product » از کتگوری « $category » تنظیم شد.

لطفا بقیه مراحل ثبت محصول را تا آخر بروید تا محصول فعال شود.

↙️ بازه ثبت سفارش را با استفاده از اعداد انگلیسی و در دو خط وارد کنید :
مثلا اگر بازه ثبت سفارش بین 10 تا 500 کا برای این محصول می باشد عبارت زیر را برای ربات ارسال کنید.

<code>10
500000</code>";
    message($user_id, $msg, $backMarkup);
    setStep($user_id, 'newProductRange');
} elseif ($user['step'] == 'newProductRange') {
    $category = $user['datash'];
    $product = $user['datam'];
    //>> استخراج مین و ماکس
    $explode = explode("\n", $text);
    $min = $explode[0];
    $max = $explode[1];
    test("$min - $max ");
    //>> بررسی ورودی
    if (!is_numeric($max) or !is_numeric($min) or $min == null or $max == null or $min > $max) {
        $msg = "ورودی صحیح نیست.

↙️ بازه ثبت سفارش را با استفاده از اعداد انگلیسی و در دو خط وارد کنید :
مثلا اگر بازه ثبت سفارش بین 10 تا 500 کا برای این محصول می باشد عبارت زیر را برای ربات ارسال کنید.

<code>10
500000</code>
از کیبورد و اعداد انگلیسی استفاده کنید.
مقدار حداقل نباید از حداکثر بیشتر باشد.";
        message($user_id, $msg, $backMarkup);
        die;
    }
    //>> اعمال به دیتابیس
    mysqli_query($db, "UPDATE product SET min='$min' , max='$max' WHERE name='$product' and category='$category' LIMIT 1");
    //>> درخواست بعدی
    $msg = "✅ حداقل سفارش « $min » و حداکثر سفارش « $max » برای محصول « $product » از کتگوری « $category » تنظیم شد.

لطفا بقیه مراحل ثبت محصول را تا آخر بروید تا محصول فعال شود.

↙️ توضیحات مربوط به این محصول را وارد کنید :
(این توضیحات در هنگام سفارش به مشتری نشان داده خواهد شد.)";
    message($user_id, $msg, $backMarkup);
    setStep($user_id, 'newProductDes');
} elseif ($user['step'] == 'newProductDes') {
    $category = $user['datash'];
    $product = $user['datam'];
    //>> اعمال به دیتابیس
    mysqli_query($db, "UPDATE product SET des='$text' WHERE name='$product' and category='$category' LIMIT 1");
    //>> درخواست بعدی
    $msg = "✅ متن 
« $text »
به عنوان توضیح برای محصول « $product » از کتگوری « $category » تنظیم شد.

لطفا بقیه مراحل ثبت محصول را تا آخر بروید تا محصول فعال شود.

یکی از وبسرویس های زیر را انتخاب کنید:";

    $apis = mysqli_query($db, "SELECT * FROM apis");

    //>> کیبورد محصولات
    $markup = array('keyboard' => array(), 'resize_keyboard' => true);
    $markup['keyboard'][] = array($adminNoAPI);
    while ($api = mysqli_fetch_assoc($apis)) {
        $name = $api['name'];
        $markup['keyboard'][] = array("$name");
    }
    $markup['keyboard'][] = array($adminBack);
    $markup = json_encode($markup);
    //>> نمایش به کاربر
    message($user_id, $msg, $markup);

    setStep($user_id, 'newProductAPI');
} elseif ($user['step'] == "newProductAPI") {
    $category = $user['datash'];
    $product = $user['datam'];
    if ($text == $adminNoAPI) {
        $api = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM apis WHERE name='$text'"))['id'];
        //>> اعمال به دیتابیس
        mysqli_query($db, "UPDATE product SET api='0' , code='0' WHERE name='$product' and category='$category' LIMIT 1");
        //>> درخواست بعدی
        $msg = "✅حالت دستی برای محصول « $product » از کتگوری « $category » تنظیم شد.

لطفا بقیه مراحل ثبت محصول را تا آخر بروید تا محصول فعال شود.

↙️ عدد اولویت نمایش محصول را وارد کنید :
(هر محصولی که اولویت بیشتری داشته باشد در قسمت بالاتری از فهرست نشان داده می شود. اگر نمیدانید چه عددی بگذارید عدد 1000 را برای ربات ارسال کنید.)
";
        message($user_id, $msg, $backMarkup);
        setStep($user_id, 'newProductSort');
    } else {
        $api = mysqli_query($db, "SELECT * FROM apis WHERE name = '$text'");
        if (mysqli_num_rows($api) < 1) {
            $msg = "وبسرویسی با این نام در دیتابیس موجود نیست!

لطفا دوباره تلاش کنید:";

            $apis = mysqli_query($db, "SELECT * FROM apis");

            //>> کیبورد محصولات
            $markup = array('keyboard' => array(), 'resize_keyboard' => true);
            while ($pro = mysqli_fetch_assoc($apis)) {
                $name = $pro['name'];
                $markup['keyboard'][] = array("$name");
            }
            $markup['keyboard'][] = array($adminBack);
            $markup = json_encode($markup);

            message($user_id, $msg, $markup);
            die;
        }

        $api = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM apis WHERE name='$text'"))['id'];
        //>> اعمال به دیتابیس
        mysqli_query($db, "UPDATE product SET api='$api' WHERE name='$product' and category='$category' LIMIT 1");
        //>> درخواست بعدی
        $msg = "✅ وبسرویس
« $text | $api »
به عنوان API برای محصول « $product » از کتگوری « $category » تنظیم شد.

لطفا بقیه مراحل ثبت محصول را تا آخر بروید تا محصول فعال شود.

↙️ کد این محصول در وبسرویس را وارد کنید:
";
        message($user_id, $msg, $backMarkup);
        setStep($user_id, 'newProductSid');
    }


} elseif ($user['step'] == 'newProductSid') {
    $category = $user['datash'];
    $product = $user['datam'];
    //>> اعمال به دیتابیس
    mysqli_query($db, "UPDATE product SET code='$text' WHERE name='$product' and category='$category' LIMIT 1");
    //>> درخواست بعدی
    $msg = "✅ شماره
« $text »
به عنوان شماره سرویس برای محصول « $product » از کتگوری « $category » تنظیم شد.

لطفا بقیه مراحل ثبت محصول را تا آخر بروید تا محصول فعال شود.

↙️ عدد اولویت نمایش محصول را وارد کنید :
(هر محصولی که اولویت بیشتری داشته باشد در قسمت بالاتری از فهرست نشان داده می شود. اگر نمیدانید چه عددی بگذارید عدد 1000 را برای ربات ارسال کنید.)";
    message($user_id, $msg, $backMarkup);
    setStep($user_id, 'newProductSort');
} elseif ($user['step'] == 'newProductSort') {
    $category = $user['datash'];
    $product = $user['datam'];
    //>> اعمال به دیتابیس
    mysqli_query($db, "UPDATE product SET sort='$text', status=1 WHERE name='$product' and category='$category' LIMIT 1");
    //>> درخواست بعدی
    $msg = "
✅ محصول شما با موفقیت ثبت شد و در دسته بندی مورد نظر قابل مشاهده است.
";
    message($user_id, $msg, $adminMarkup);
    setStep($user_id, 'home');
} elseif ($text == $adminEdit && in_array($user_id, $_adminList)) {
    //>> کیبورد محصولات
    $products = mysqli_query($db, "SELECT * FROM product");
    $markup = array('keyboard' => array(), 'resize_keyboard' => true);
    while ($pro = mysqli_fetch_assoc($products)) {
        $id = $pro['id'];
        $product = $pro['name'];
        $category = $pro['category'];
        $markup['keyboard'][] = array("$id | $product | $category");
    }
    $markup['keyboard'][] = array($adminBack);
    $markup = json_encode($markup);
    //>> نمایش به کاربر
    $msg = "
از این بخش برای ویرایش محصولاتی که قبلا در ربات تعریف شده استفاده کنید. نام محصول را انتخاب کنید و با توجه به راهنمای ربات اطلاعات محصول را مجددا وارد کنید. حتما تا آخرین مرحله ویرایش محصول پیش بروید تا محصول فعال شود.

⚙️ یکی از محصولات زیر را انتخاب کنید :";
    message($user_id, $msg, $markup);
    setStep($user_id, 'editProduct');
} elseif ($user['step'] == 'editProduct') {
    //>> استخراج محصول و کتگوری
    $explode = explode(" | ", $text);
    $pid = $explode[0];

    $pro = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE id='$pid'"));
    $product = $pro['name'];
    $category = $pro['category'];
    //>> بررسی ادمین مولتی
    $creator = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE name='$product' LIMIT 1"))['creator'];
    //>> تغییرات دیتابیس
    mysqli_query($db, "UPDATE user SET datash='$category' , datam='$product' WHERE uid='$user_id' LIMIT 1");
    //>> پیام به کاربر
    $msg = "محصول: $product
دسته بندی: $category

کی از گزینه های زیر را انتخاب کنید :";
    if ($pro['status'] == 0) {
        $markup = json_encode(
            array(
                'keyboard' => array(
                    array($onProduct),
                    array($editProductPrice, $editProductName, $editProductRange),
                    array($editProduct, $editProductApicode),
                    array($editProductApi, $editProductDescription),
                    array($deleteProduct, $editProductCategory),
                    array($adminBack)
                ),
                'resize_keyboard' => true
            )
        );
    } else {
        $markup = json_encode(
            array(
                'keyboard' => array(
                    array($offProduct),
                    array($editProductPrice, $editProductName, $editProductRange),
                    array($editProduct, $editProductApicode),
                    array($editProductApi, $editProductDescription),
                    array($deleteProduct, $editProductCategory),
                    array($adminBack)
                ),
                'resize_keyboard' => true
            )
        );
    }
    message($user_id, $msg, $markup);
    setStep($user_id, 'productEditOrDelete');
    die;
} elseif ($user['step'] == 'productEditOrDelete') {
    //>> پارامتر ها
    $name = $user['datam'];
    $category = $user['datash'];
    //>> حذف محصول
    if ($text == $deleteProduct) {
        //>> تغییرات دیتابیس
        mysqli_query($db, "DELETE FROM product WHERE name='$name' and category='$category'");

        //>> کیبورد محصولات
        $products = mysqli_query($db, "SELECT * FROM product");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($pro = mysqli_fetch_assoc($products)) {
            $product = $pro['name'];
            $category = $pro['category'];
            $id = $pro['id'];
            $markup['keyboard'][] = array("$id | $product | $category");
        }
        $markup['keyboard'][] = array($adminBack);
        $markup = json_encode($markup);
        //>> اطلاع به کاربر
        $msg = "🗑 محصول « $name » از کتگوری « $category » با موفقیت حذف شد.";
        message($user_id, $msg, $markup);
        setStep($user_id, 'editProduct');
        die;
    }
    //>> تغییر کتگوری
    elseif ($text == $editProductCategory) {
        //>> ساخت کیبورد کتگوری ها
        $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='0'");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($catName = mysqli_fetch_assoc($cat)['name']) {
            $markup['keyboard'][] = array($catName);
        }
        $markup['keyboard'][] = array($back);
        $markup = json_encode($markup);

        //>> ارسال به کاربر
        $msg = "⚙️ لطفا یکی از گزینه های پایین را انتخاب کنید:";
        message($user_id, $msg, $markup);
        setStep($user_id, 'ecategory');
        die;
    }
    //>> ادیت محصول
    elseif ($text == $editProduct) {
        $msg = "✅ شما در حال ویرایش محصول « $name » از کتگوری « $category » می باشید.

لطفا بقیه مراحل ویرایش محصول را تا آخر بروید تا محصول فعال شود.

↙️ قیمت هر عدد از این محصول را وارد کنید :
(مثلا اگر قیمت هر کا از این محصول 2500 تومان است عدد 2.5 را وارد کنید.)";
        message($user_id, $msg);
        setStep($user_id, 'newProductPrice');
        die;
    } elseif ($text == $onProduct) {
        //>> تغییرات دیتابیس
        mysqli_query($db, "UPDATE product SET status = 1 WHERE name='$name' and category='$category'");

        //>> کیبورد محصولات
        $products = mysqli_query($db, "SELECT * FROM product");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($pro = mysqli_fetch_assoc($products)) {
            $product = $pro['name'];
            $category = $pro['category'];
            $id = $pro['id'];
            $markup['keyboard'][] = array("$id | $product | $category");
        }
        $markup['keyboard'][] = array($adminBack);
        $markup = json_encode($markup);
        //>> اطلاع به کاربر
        $msg = "🟢 محصول « $name » از کتگوری « $category » با موفقیت روشن شد.";
        message($user_id, $msg, $markup);
        setStep($user_id, 'editProduct');
        die;
    } elseif ($text == $offProduct) {
        //>> تغییرات دیتابیس
        mysqli_query($db, "UPDATE product SET status = 0 WHERE name='$name' and category='$category'");

        //>> کیبورد محصولات
        $products = mysqli_query($db, "SELECT * FROM product");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($pro = mysqli_fetch_assoc($products)) {
            $product = $pro['name'];
            $category = $pro['category'];
            $id = $pro['id'];
            $markup['keyboard'][] = array("$id | $product | $category");
        }
        $markup['keyboard'][] = array($adminBack);
        $markup = json_encode($markup);
        //>> اطلاع به کاربر
        $msg = "🟡 محصول « $name » از کتگوری « $category » با موفقیت خاموش شد.";
        message($user_id, $msg, $markup);
        setStep($user_id, 'editProduct');
        die;
    } elseif ($text == $editProductDescription) {
        $des = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE name='$name' and category='$category'"))['des'];
        $msg = "توضیحات محصول در حال حاضر:

$des

برای تغییر، توضیحات جدید را وارد کنید:
        ";
        message($user_id, $msg, $adminBackMarkup);
        setStep($user_id, "editProductDescription");
        die;
    } elseif ($text == $editProductPrice) {
        $price = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE name='$name' and category='$category'"))['price'] * 1000;
        $msg = "قیمت محصول در حال حاضر:

$price

برای تغییر، توضیحات جدید را وارد کنید:
        ";
        message($user_id, $msg, $adminBackMarkup);
        setStep($user_id, "editProductPrice");
        die;
    } elseif ($text == $editProductName) {
        $price = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE name='$name' and category='$category'"))['name'];
        $msg = "نام محصول در حال حاضر:

$price

برای تغییر، مقدار جدید را وارد کنید:
        ";
        message($user_id, $msg, $adminBackMarkup);
        setStep($user_id, "editProductName");
        die;
    } elseif ($text == $editProductApi) {
        $api = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE name='$name' and category='$category'"))['api'];
        if ($api > 0) {
            $api = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM apis WHERE id = $api"))['name'];
        } else {
            $api = "دستی - بدون وبسرویس";
        }
        $msg = "قیمت محصول در حال حاضر:

$api

برای تغییر،یکی از گزینه های زیر را انتخاب کنید:
        ";

        $apis = mysqli_query($db, "SELECT * FROM apis");

        //>> کیبورد محصولات
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        $markup['keyboard'][] = array($adminNoAPI);
        while ($api = mysqli_fetch_assoc($apis)) {
            $name = $api['name'];
            $markup['keyboard'][] = array("$name");
        }
        $markup['keyboard'][] = array($adminBack);
        $markup = json_encode($markup);
        message($user_id, $msg, $markup);
        setStep($user_id, "editProductApi");
        die;
    } elseif ($text == $editProductApicode) {
        $price = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE name='$name' and category='$category'"))['code'];
        $msg = "کد محصول در حال حاضر:

$price

برای تغییر، مقدار جدید را وارد کنید:
        ";
        message($user_id, $msg, $adminBackMarkup);
        setStep($user_id, "editProductApicode");
        die;
    } elseif ($text == $editProductRange) {

        $min = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE name='$name' and category='$category'"))['min'];
        $max = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE name='$name' and category='$category'"))['max'];
        $msg = "بازه سفارش محصول در حال حاضر:

$min
$max

برای تغییر، مقدار جدید را وارد کنید:
لطفا مقدار مورد نظر را در دو خط وارد

<code>مقدار حداقل
مقدار حداکثر</code>

";
        message($user_id, $msg, $adminBackMarkup);
        setStep($user_id, "editProductRange");
        die;
    }
} elseif ($user['step'] == "editProductPrice") {
    //>> تغییرات دیتابیس
    //>> پارامتر ها
    $name = $user['datam'];
    $category = $user['datash'];
    $price = $text / 1000;
    mysqli_query($db, "UPDATE product SET price = '$price' WHERE name='$name' and category='$category'");

    //>> کیبورد محصولات
    $products = mysqli_query($db, "SELECT * FROM product");
    $markup = array('keyboard' => array(), 'resize_keyboard' => true);
    while ($pro = mysqli_fetch_assoc($products)) {
        $product = $pro['name'];
        $category = $pro['category'];
        $id = $pro['id'];
        $markup['keyboard'][] = array("$id | $product | $category");
    }
    $markup['keyboard'][] = array($adminBack);
    $markup = json_encode($markup);
    //>> اطلاع به کاربر
    $msg = "💬 مبلغ:

$text

به عنوان قیمت محصول « $name » از کتگوری « $category » ثبت شد.";
    $pro = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE name='$product' and category='$category'"));
    if ($pro['status'] == 0) {
        $markup = json_encode(
            array(
                'keyboard' => array(
                    array($onProduct),
                    array($editProductPrice, $editProductName, $editProductRange),
                    array($editProduct, $editProductApicode),
                    array($editProductApi, $editProductDescription),
                    array($deleteProduct, $editProductCategory),
                    array($adminBack)
                ),
                'resize_keyboard' => true
            )
        );
    } else {
        $markup = json_encode(
            array(
                'keyboard' => array(
                    array($offProduct),
                    array($editProductPrice, $editProductName, $editProductRange),
                    array($editProduct, $editProductApicode),
                    array($editProductApi, $editProductDescription),
                    array($deleteProduct, $editProductCategory),
                    array($adminBack)
                ),
                'resize_keyboard' => true
            )
        );
    }
    message($user_id, $msg, $markup);
    setStep($user_id, 'productEditOrDelete');
    die;
} elseif ($user['step'] == "editProductName") {
    //>> تغییرات دیتابیس
    //>> پارامتر ها
    $name = $user['datam'];
    $category = $user['datash'];
    mysqli_query($db, "UPDATE product SET name = '$text' WHERE name='$name' and category='$category'");


    //>> اطلاع به کاربر
    $msg = "💬 مقدار:

$text

به عنوان نام محصول « $name » از کتگوری « $category » ثبت شد.";
    mysqli_query($db, "UPDATE user SET datam='$text' WHERE uid='$user_id' LIMIT 1");
    $pro = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE name='$name' and category='$category'"));
    if ($pro['status'] == 0) {
        $markup = json_encode(
            array(
                'keyboard' => array(
                    array($onProduct),
                    array($editProductPrice, $editProductName, $editProductRange),
                    array($editProduct, $editProductApicode),
                    array($editProductApi, $editProductDescription),
                    array($deleteProduct, $editProductCategory),
                    array($adminBack)
                ),
                'resize_keyboard' => true
            )
        );
    } else {
        $markup = json_encode(
            array(
                'keyboard' => array(
                    array($offProduct),
                    array($editProductPrice, $editProductName, $editProductRange),
                    array($editProduct, $editProductApicode),
                    array($editProductApi, $editProductDescription),
                    array($deleteProduct, $editProductCategory),
                    array($adminBack)
                ),
                'resize_keyboard' => true
            )
        );
    }
    message($user_id, $msg, $markup);
    setStep($user_id, 'productEditOrDelete');
    die;
} elseif ($user['step'] == "editProductApicode") {
    //>> تغییرات دیتابیس
    //>> پارامتر ها
    $name = $user['datam'];
    $category = $user['datash'];
    mysqli_query($db, "UPDATE product SET code = '$text' WHERE name='$name' and category='$category'");


    //>> اطلاع به کاربر
    $msg = "💬 مقدار:

$text

به عنوان کد وبسرویس محصول « $name » از کتگوری « $category » ثبت شد.";
    $pro = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE name='$product' and category='$category'"));
    if ($pro['status'] == 0) {
        $markup = json_encode(
            array(
                'keyboard' => array(
                    array($onProduct),
                    array($editProductPrice, $editProductName, $editProductRange),
                    array($editProduct, $editProductApicode),
                    array($editProductApi, $editProductDescription),
                    array($deleteProduct, $editProductCategory),
                    array($adminBack)
                ),
                'resize_keyboard' => true
            )
        );
    } else {
        $markup = json_encode(
            array(
                'keyboard' => array(
                    array($offProduct),
                    array($editProductPrice, $editProductName, $editProductRange),
                    array($editProduct, $editProductApicode),
                    array($editProductApi, $editProductDescription),
                    array($deleteProduct, $editProductCategory),
                    array($adminBack)
                ),
                'resize_keyboard' => true
            )
        );
    }
    message($user_id, $msg, $markup);
    setStep($user_id, 'productEditOrDelete');
    die;
} elseif ($user['step'] == "editProductRange") {
    //>> تغییرات دیتابیس
    //>> پارامتر ها
    $name = $user['datam'];
    $category = $user['datash'];
    //>> استخراج مین و ماکس
    $explode = explode("\n", $text);
    $min = $explode[0];
    $max = $explode[1];
    test("$min - $max ");
    //>> بررسی ورودی
    if (!is_numeric($max) or !is_numeric($min) or $min == null or $max == null or $min > $max) {
        $msg = "ورودی صحیح نیست.

↙️ بازه ثبت سفارش را با استفاده از اعداد انگلیسی و در دو خط وارد کنید :
مثلا اگر بازه ثبت سفارش بین 10 تا 500 کا برای این محصول می باشد عبارت زیر را برای ربات ارسال کنید.

<code>10
500000</code>
از کیبورد و اعداد انگلیسی استفاده کنید.
مقدار حداقل نباید از حداکثر بیشتر باشد.";
        message($user_id, $msg, $backMarkup);
        die;
    }
    mysqli_query($db, "UPDATE product SET min='$min' , max='$max' WHERE name='$name' and category='$category' LIMIT 1");

    //>> اطلاع به کاربر
    $msg = "✅ حداقل سفارش « $min » و حداکثر سفارش « $max » برای محصول « $product » از کتگوری « $category » تنظیم شد.";
    $pro = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE name='$product' and category='$category'"));
    if ($pro['status'] == 0) {
        $markup = json_encode(
            array(
                'keyboard' => array(
                    array($onProduct),
                    array($editProductPrice, $editProductName, $editProductRange),
                    array($editProduct, $editProductApicode),
                    array($editProductApi, $editProductDescription),
                    array($deleteProduct, $editProductCategory),
                    array($adminBack)
                ),
                'resize_keyboard' => true
            )
        );
    } else {
        $markup = json_encode(
            array(
                'keyboard' => array(
                    array($offProduct),
                    array($editProductPrice, $editProductName, $editProductRange),
                    array($editProduct, $editProductApicode),
                    array($editProductApi, $editProductDescription),
                    array($deleteProduct, $editProductCategory),
                    array($adminBack)
                ),
                'resize_keyboard' => true
            )
        );
    }
    message($user_id, $msg, $markup);
    setStep($user_id, 'productEditOrDelete');
    die;
} elseif ($user['step'] == "editProductApi") {
    //>> تغییرات دیتابیس
    //>> پارامتر ها
    $name = $user['datam'];
    $category = $user['datash'];

    $api = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM apis WHERE name='$text'"))['id'];
    //>> اعمال به دیتابیس
    mysqli_query($db, "UPDATE product SET api='$api' WHERE name='$name' and category='$category' LIMIT 1");
    //>> درخواست بعدی
    $msg = "✅ وبسرویس
« $text | $api »
به عنوان API برای محصول « $product » از کتگوری « $category » تنظیم شد.
";
    $pro = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE name='$product' and category='$category'"));
    if ($pro['status'] == 0) {
        $markup = json_encode(
            array(
                'keyboard' => array(
                    array($onProduct),
                    array($editProductPrice, $editProductName, $editProductRange),
                    array($editProduct, $editProductApicode),
                    array($editProductApi, $editProductDescription),
                    array($deleteProduct, $editProductCategory),
                    array($adminBack)
                ),
                'resize_keyboard' => true
            )
        );
    } else {
        $markup = json_encode(
            array(
                'keyboard' => array(
                    array($offProduct),
                    array($editProductPrice, $editProductName, $editProductRange),
                    array($editProduct, $editProductApicode),
                    array($editProductApi, $editProductDescription),
                    array($deleteProduct, $editProductCategory),
                    array($adminBack)
                ),
                'resize_keyboard' => true
            )
        );
    }
    message($user_id, $msg, $markup);
    setStep($user_id, 'productEditOrDelete');
    die;
} elseif ($user['step'] == "editProductDescription") {
    //>> تغییرات دیتابیس
    //>> پارامتر ها
    $name = $user['datam'];
    $category = $user['datash'];
    mysqli_query($db, "UPDATE product SET des = '$text' WHERE name='$name' and category='$category'");

    //>> کیبورد محصولات
    $products = mysqli_query($db, "SELECT * FROM product");
    $markup = array('keyboard' => array(), 'resize_keyboard' => true);
    while ($pro = mysqli_fetch_assoc($products)) {
        $product = $pro['name'];
        $category = $pro['category'];
        $id = $pro['id'];
        $markup['keyboard'][] = array("$id | $product | $category");
    }
    $markup['keyboard'][] = array($adminBack);
    $markup = json_encode($markup);
    //>> اطلاع به کاربر
    $msg = "💬 متن:

$text

به عنوان توضیحات محصول « $name » از کتگوری « $category » ثبت شد.";
    message($user_id, $msg, $markup);
    setStep($user_id, 'editProduct');
    die;
} elseif ($user['step'] == 'ecategory') {
    //>> برگشت به کتگوری اصلی
    $name = $user['datash'];
    $parent = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM category WHERE name='$name' LIMIT 1"))['parent'];
    if ($text == $bacCat && $parent == '0') {
        //>> ساخت کیبورد کتگوری ها
        $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='0'");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($catname = mysqli_fetch_assoc($cat)['name']) {
            $markup['keyboard'][] = array($catname);
        }
        $markup['keyboard'][] = array($adminBack);
        $markup = json_encode($markup);
        //>> ارسال به کاربر
        $msg = "یکی از گزینه های زیر را انتخاب کنید";
        message($user_id, $msg, $markup);
        setStep($user_id, 'ecategory');
        die;
    }
    //>> برگشت
    if ($text == $bacCat && $parent !== '0') {
        $text = $parent;
        //>> ساخت کیبورد کتگوری
        $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='$text'");
        mysqli_query($db, "UPDATE user SET datash='$parent'  WHERE uid='$user_id'");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        if (mysqli_num_rows($cat) > 0) {
            while ($catname = mysqli_fetch_assoc($cat)['name']) {
                $markup['keyboard'][] = array($catname);
            }
        }
        $markup['keyboard'][] = array($adminBack, $bacCat);
        $markup = json_encode($markup);
        $msg = "یکی از گزینه های زیر را انتخاب کنید";
        message($user_id, $msg, $markup);
        setStep($user_id, 'ecategory');
        die;

    }
    //>> بررسی ورودی
    $cat = mysqli_query($db, "SELECT * FROM category WHERE name='$text'");
    if (mysqli_num_rows($cat) == 0) {
        $msg = "❌ مقدار ورودی صحیح نیست.
لطفا فقط از گزینه های ربات برای انتخاب دسته محصول استفاده کنید.";
        message($user_id, $msg);
        die;
    }
    //>> ساخت کیبورد کتگوری
    $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='$text'");
    if (mysqli_num_rows($cat) > 0) {
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($catname = mysqli_fetch_assoc($cat)['name']) {
            $markup['keyboard'][] = array($catname);
        }
        $markup['keyboard'][] = array($adminBack, $bacCat);
        $markup = json_encode($markup);
        //>> ارسال به کاربر
        $msg = "لطفا دسته بندی محصول خود را از بین موارد پایین انتخاب کنید:";
        message($user_id, $msg, $markup);
        setStep($user_id, 'ecategory');
        die;
    }

    //>> رسیدن به آخرین زیردسته
    $product = $user['datam'];
    mysqli_query($db, "UPDATE product SET category='$text' WHERE name='$product'");

    //>> اطلاع به کاربر
    $msg = "✅ کتگوری « $text » برای محصول « $product » تنظیم شد.

یکی از گزینه های زیر را انتخاب کنید:";
    message($user_id, $msg, $adminMarkup);
    setStep($user_id, 'none');
} elseif ($text == $adminEditCat && in_array($user_id, $_adminList)) {
    //>> کیبورد محصولات
    $products = mysqli_query($db, "SELECT * FROM category");
    $markup = array('keyboard' => array(), 'resize_keyboard' => true);
    while ($pro = mysqli_fetch_assoc($products)) {
        $category = $pro['name'];
        $markup['keyboard'][] = array("$category");
    }
    $markup['keyboard'][] = array($adminBack);
    $markup = json_encode($markup);
    //>> نمایش به کاربر
    $msg = "
⚙️ برای ویرایش نام دسته بندی ها می توانید از این قسمت استفاده کنید.

⚙️ یکی از دسته بندی های زیر را انتخاب کنید :";
    message($user_id, $msg, $markup);
    setStep($user_id, 'editCatname');
} elseif ($user['step'] == 'editCatname') {
    //>> استخراج محصول و کتگوری
    $category = $text;
    //>> تغییرات دیتابیس
    mysqli_query($db, "UPDATE user SET datash='$category' WHERE uid='$user_id' LIMIT 1");
    //>> پیام به کاربر
    $APIname = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM category WHERE name='$category' LIMIT 1"))['name'];
    $msg = "نام تنظیم شده :
<code>$APIname</code>
برای تغییر نام جدید را وارد کنید :";
    $markup = json_encode(array('keyboard' => array(array($adminBack)), 'resize_keyboard' => true));
    message($user_id, $msg, $markup);
    setStep($user_id, 'editCatnameFinal');
    die;
} elseif ($user['step'] == 'editCatnameFinal') {
    $category = $user['datash'];

    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM category WHERE name='$text'"));
    if ($num > 0) {
        $msg = "نام تکراری است!
دوباره تلاش کنید:";
        message($user_id, $msg, $adminBackMarkup);
        die;
    }
    //>> اعمال به دیتابیس
    mysqli_query($db, "UPDATE category SET name='$text' WHERE name='$category'");
    mysqli_query($db, "UPDATE product SET category='$text' WHERE category='$category'");

    //>> اطلاع به کاربر
    //>> کیبورد محصولات
    $products = mysqli_query($db, "SELECT * FROM category");
    $markup = array('keyboard' => array(), 'resize_keyboard' => true);
    while ($pro = mysqli_fetch_assoc($products)) {
        $category = $pro['name'];
        $markup['keyboard'][] = array("$category");
    }
    $markup['keyboard'][] = array($adminBack);
    $markup = json_encode($markup);
    //>> نمایش به کاربر
    $msg = "تغییرات با موفقیت اعمال شد.

⚙️ یکی از دسته بندی های زیر را انتخاب کنید :";
    message($user_id, $msg, $markup);
    setStep($user_id, 'editCatname');
} elseif ($text == $adminCatNew && in_array($user_id, $_adminList)) {
    //>> نمایش به کاربر
    $msg = "
⚙️ در این قسمت می توانید یک دسته بندی جدید به ربات اضافه کنید.

⚙️ نام کتگوری جدید را وارد کنید :";
    $markup = array('keyboard' => array(), 'resize_keyboard' => true);
    $markup['keyboard'][] = array($adminBack);
    $markup = json_encode($markup);
    message($user_id, $msg, $markup);
    setStep($user_id, 'adminCatNew');
} elseif ($user['step'] == 'adminCatNew') {
    //>> اضافه کردن به دیتابیس
    mysqli_query($db, "INSERT INTO category(name) VALUES('$text')");
    //>> اطلاع به کاربر
    $msg = "تغییرات با موفقیت اعمال شد.

⚙️ یکی از گزینه های زیر را انتخاب کنید :";
    message($user_id, $msg, $adminMarkup);
    setStep($user_id, 'home');
    die;
} elseif ($text == $adminCatDel && in_array($user_id, $_adminList)) {
    //>> کیبورد محصولات
    $products = mysqli_query($db, "SELECT * FROM category");
    $markup = array('keyboard' => array(), 'resize_keyboard' => true);
    while ($pro = mysqli_fetch_assoc($products)) {
        $category = $pro['name'];
        $markup['keyboard'][] = array("$category");
    }
    $markup['keyboard'][] = array($adminBack);
    $markup = json_encode($markup);
    //>> نمایش به کاربر
    $msg = "
⚙️ از این قسمت برای حذف کامل یک دسته بندی استفاده کنید.

⚙️ برای حذف یکی از دسته بندی های زیر را انتخاب کنید :";
    message($user_id, $msg, $markup);
    setStep($user_id, 'editCatDel');
} elseif ($user['step'] == 'editCatDel') {
    //>> استخراج محصول و کتگوری
    $category = $text;
    //>> تغییرات دیتابیس
    mysqli_query($db, "DELETE FROM `category` WHERE name='$category'");
    //>> پیام به کاربر
    $msg = "تغییرات با موفقیت اعمال شد.

⚙️ یکی از گزینه های زیر را انتخاب کنید :";
    message($user_id, $msg, $adminMarkup);
    setStep($user_id, 'home');
    die;
} elseif ($text == $adminApisBalance && in_array($user_id, $_adminList)) {
    mysqli_query($db, "DELETE FROM apis WHERE site IS NULL");
    $apis = mysqli_query($db, "SELECT * FROM apis");
    $msg = "موجودی وبسرویس ها";

    //>> کیبورد محصولات
    $markup = array('inline_keyboard' => []);
    while ($pro = mysqli_fetch_assoc($apis)) {
        $name = $pro['name'];
        $token = $pro['token'];
        $site = $pro['site'];

        //>> GET balance
        $data = array(
            'key' => $token,
            // (دریافتی از آذر سوشال (اجباری برای همه عملیات Api      
            'action' => 'balance', // (عملیات (فقط برای ثبت سفارش اختیاری برای سایر عملیات اجباری
        );
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $site);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $result = curl_exec($ch);
        message(452323199, $result);

        $services = json_decode($result, true);
        $balance = $services['balance'];

        if ($balance == null) {
            $balance = "خطا";
        }

        $markup['inline_keyboard'][] = [
            ['text' => "$name", 'callback_data' => "doneCallbackData"],
            ['text' => "$balance", 'callback_data' => "doneCallbackData"]
        ];
    }
    $markup = json_encode($markup);
    //>> نمایش به کاربر
    message($user_id, $msg, $markup);

    $msg = "یکی از گزینه های زیر را انتخاب کنید:";
    message($user_id, $msg, $adminMarkup);
    die;
} elseif ($text == $adminAPI && in_array($user_id, $_adminList)) {
    mysqli_query($db, "DELETE FROM apis WHERE site IS NULL");
    $apis = mysqli_query($db, "SELECT * FROM apis");
    $msg = "یکی از گزینه های زیر را انتخاب کنید:";

    //>> کیبورد محصولات
    $markup = array('keyboard' => array(), 'resize_keyboard' => true);
    while ($pro = mysqli_fetch_assoc($apis)) {
        $name = $pro['name'];
        $markup['keyboard'][] = array("$name");
    }
    $markup['keyboard'][] = array($adminBack);
    $markup = json_encode($markup);
    //>> نمایش به کاربر
    message($user_id, $msg, $markup);
    setStep($user_id, 'editAPIs');
    die;
} elseif ($user['step'] == "editAPIs") {

    $api = mysqli_query($db, "SELECT * FROM apis WHERE name = '$text'");
    if (mysqli_num_rows($api) < 1) {
        $msg = "وبسرویسی با این نام در دیتابیس موجود نیست!

لطفا دوباره تلاش کنید:";

        $apis = mysqli_query($db, "SELECT * FROM apis");

        //>> کیبورد محصولات
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($pro = mysqli_fetch_assoc($apis)) {
            $name = $pro['name'];
            $markup['keyboard'][] = array("$name");
        }
        $markup['keyboard'][] = array($adminBack);
        $markup = json_encode($markup);

        message($user_id, $msg, $markup);
        die;
    }

    $api = mysqli_fetch_assoc($api);
    $idsh = $api['id'];

    mysqli_query($db, "UPDATE user SET datash='$idsh' WHERE uid='$user_id' LIMIT 1");

    $msg = "وبسرویس انتخاب شده: $text

یکی از گزینه های زیر را انتخاب کنید:";
    message($user_id, $msg, $apiMarkup);
    setStep($user_id, "editApi_$idsh");
} elseif (strpos($user['step'], "editApi_") !== false) {
    $apiid = explode("_", $user['step'])[1];

    if ($text == $apiDelete) {
        mysqli_query($db, "DELETE FROM apis WHERE id = $apiid");
        mysqli_query($db, "UPDATE product SET api = 0 WHERE api = $apiid");

        $msg = "وبسرویس مورد نظر با موفقیت حذف شد. تمامی محصولات این وبسرویس به حالت دستی تغییر کرد.
        
یکی از گزینه های زیر را انتخاب کنید:";
        message($user_id, $msg, $adminMarkup);
        setStep($user_id, "home");
        die;
    } elseif ($text == $apiDeleteAll) {
        mysqli_query($db, "DELETE FROM apis WHERE id = $apiid");
        mysqli_query($db, "DELETE FROM product WHERE api = $apiid");

        $msg = "وبسرویس مورد نظر با موفقیت حذف شد. تمامی محصولات این وبسرویس هم از دیتابیس حذف شد.
        
یکی از گزینه های زیر را انتخاب کنید:";
        message($user_id, $msg, $adminMarkup);
        setStep($user_id, "home");
        die;
    } elseif ($text == $apiUpdate) {
        $msg = "چند لحظه منتظر بمانید...";
        message($user_id, $msg);
        file_get_contents("$_host/doc/updateApi.php?api=$apiid&type=simple");
        $msg = "وبسرویس با موفقیت آپدیت شد.";
        message($user_id, $msg, $adminMarkup);
        setStep($user_id, "home");
        die;
    } elseif ($text == $apiUpdateAll) {
        $msg = "درصد افزایش قیمت را وارد کنید:

برای مثال 30";
        message($user_id, $msg, $adminBackMarkup);
        setStep($user_id, "apiUpdateAll");
        die;
    } elseif ($text == $apiChangeToken) {
        mysqli_query($db, "UPDATE user SET datash='$idsh' WHERE uid='$user_id' LIMIT 1");
        $msg = "توکن جدید را وارد کنید:";

        message($user_id, $msg, $adminBackMarkup);
        setStep($user_id, "apiTokenChange");
        die;
    } else {
        $msg = "دستور وارد شده صحیح نیست!

یکی از گزینه های زیر را انتخاب کنید:";
        message($user_id, $msg, $adminMarkup);
        setStep($user_id, "home");
        die;
    }
} elseif ($user['step'] == "apiUpdateAll") {
    $apiid = $user['datash'];
    if (!is_numeric($text)) {
        $msg = "درصد وارد شده صحیح نیست!

دوباره تلاش کنید:";
        message($user_id, $msg, $adminBackMarkup);
    }
    $msg = "چند لحظه منتظر بمانید...";
    message($user_id, $msg);
    file_get_contents("$_host/doc/updateApi.php?api=$apiid&type=all&percent=$text");
    $msg = "وبسرویس با موفقیت آپدیت شد.";
    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;
} elseif ($user['step'] == "apiChangeToken") {
    $apiid = $user['datash'];
    mysqli_query($db, "UPDATE apis SET token='$text' WHERE id='$apiid'");
    $msg = "تنظیم توکن جدید با موفقیت انجام شد.";
    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;
} elseif ($text == $adminNewAPI && in_array($user_id, $_adminList)) {
    $msg = "نام وبسرویس جدید را وارد کنید:";
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "SetApiName");
    mysqli_query($db, "DELETE FROM apis WHERE site IS NULL");
    die;
} elseif ($user['step'] == "SetApiName") {

    if (mysqli_num_rows(mysqli_query($db, "SELECT * FROM apis WHERE name='$text'")) > 0) {
        $msg = "وبسرویسی با این نام در دیتابیس موجود است. لطفا دوباره تلاش کنید:";
        $apis = mysqli_query($db, "SELECT * FROM apis");

        //>> کیبورد محصولات
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($pro = mysqli_fetch_assoc($apis)) {
            $name = $pro['name'];
            $markup['keyboard'][] = array("$name");
        }
        $markup['keyboard'][] = array($adminBack);
        $markup = json_encode($markup);

        message($user_id, $msg, $markup);
        die;
    }

    $res = mysqli_query($db, "INSERT INTO apis(name) VALUES('$text')");
    $inserted_id = mysqli_insert_id($db);

    $msg = "کلید وبسرویس $text را وارد کنید:";
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "SetApiToken");
    mysqli_query($db, "UPDATE user SET datash='$inserted_id' WHERE uid='$user_id'");
    die;
} elseif ($user['step'] == "SetApiToken") {
    $apiid = $user['datash'];
    mysqli_query($db, "UPDATE apis SET token='$text' WHERE id='$apiid'");

    $msg = "آدرس وبسرویس را وارد کنید:";
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "SetApiSite");
    die;
} elseif ($user['step'] == "SetApiSite") {
    $apiid = $user['datash'];

    $api = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM apis WHERE id = $apiid"));

    $token = $api['token'];
    $site = $text;

    //>> GET balance
    $data = array(
        'key' => $token,
        // (دریافتی از آذر سوشال (اجباری برای همه عملیات Api      
        'action' => 'balance', // (عملیات (فقط برای ثبت سفارش اختیاری برای سایر عملیات اجباری
    );
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $text);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    message(452323199, $result);

    $services = json_decode($result, true);

    if ($services['balance'] == null) {

        test(json_encode($services));
        $msg = "مشخصات وارد شده صحیح نیست!

دوباره تلاش کنید:";
    } else {


        $msg = "وبسرویس مورد نظر با موفقیت به دیتابیس اضافه شد. از بخش $adminAPI می توانید برای اضافه کردن یا آپدیت وبسرویس اقدام کنید

موجودی شما در وبسرویس: " . $services['balance'];
        mysqli_query($db, "UPDATE apis SET site='$text' , status = 1 WHERE id='$apiid'");

    }
    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;

} elseif ($text == $adminReportChannel && in_array($user_id, $_adminList)) {
    $get = file_get_contents("doc/adminReportChannel.txt");
    $msg = "کانال تنظیم شده در حال حاضر :
$get

برای تغییر کانال لطفا یک پست از کانال جدید برای ربات ارسال نمایید: توجه داشته باشید که ربات باید حتما ادمین کانال مورد نظر باشد
    ";
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "adminReportChannel");
} elseif ($user['step'] == "adminReportChannel") {
    $forward_from_chat = $update['message']['forward_from_chat']['id'];
    if ($forward_from_chat == null)
        $forward_from_chat = $text;
    file_put_contents("doc/adminReportChannel.txt", $forward_from_chat);

    $msg = "✅ تنظیمات با موفقیت انجام شد.";

    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;
} elseif ($text == $adminPaymentChannel && in_array($user_id, $_adminList)) {
    $get = file_get_contents("doc/adminPaymentChannel.txt");
    $msg = "کانال تنظیم شده در حال حاضر :
$get

برای تغییر کانال لطفا یک پست از کانال جدید برای ربات ارسال نمایید: توجه داشته باشید که ربات باید حتما ادمین کانال مورد نظر باشد
    ";
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "adminPaymentChannel");
} elseif ($user['step'] == "adminPaymentChannel") {
    $forward_from_chat = $update['message']['forward_from_chat']['id'];
    if ($forward_from_chat == null)
        $forward_from_chat = $text;
    file_put_contents("doc/adminPaymentChannel.txt", $forward_from_chat);

    $msg = "✅ تنظیمات با موفقیت انجام شد.";

    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;
} elseif ($text == $adminTicketChannel && in_array($user_id, $_adminList)) {
    $get = file_get_contents("doc/adminTicketChannel.txt");
    $msg = "کانال تنظیم شده در حال حاضر :
$get

برای تغییر کانال لطفا یک پست از کانال جدید برای ربات ارسال نمایید: توجه داشته باشید که ربات باید حتما ادمین کانال مورد نظر باشد
    ";
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "adminTicketChannel");
} elseif ($user['step'] == "adminTicketChannel") {
    $forward_from_chat = $update['message']['forward_from_chat']['id'];
    if ($forward_from_chat == null)
        $forward_from_chat = $text;
    file_put_contents("doc/adminTicketChannel.txt", $forward_from_chat);

    $msg = "✅ تنظیمات با موفقیت انجام شد.";

    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;
} elseif ($text == $adminAPIReportChannel && in_array($user_id, $_adminList)) {
    $get = file_get_contents("doc/adminAPIReportChannel.txt");
    $msg = "کانال تنظیم شده در حال حاضر :
$get

برای تغییر کانال لطفا یک پست از کانال جدید برای ربات ارسال نمایید: توجه داشته باشید که ربات باید حتما ادمین کانال مورد نظر باشد
    ";
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "adminAPIReportChannel");
} elseif ($user['step'] == "adminAPIReportChannel") {
    $forward_from_chat = $update['message']['forward_from_chat']['id'];
    if ($forward_from_chat == null)
        $forward_from_chat = $text;
    file_put_contents("doc/adminAPIReportChannel.txt", $forward_from_chat);

    $msg = "✅ تنظیمات با موفقیت انجام شد.";

    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;
} elseif ($text == $adminJoinChannel && in_array($user_id, $_adminList)) {
    $get = file_get_contents("doc/adminJoinChannel.txt");
    $msg = "کانال تنظیم شده در حال حاضر :
$get

برای تغییر کانال لطفا یک پست از کانال جدید برای ربات ارسال نمایید: توجه داشته باشید که ربات باید حتما ادمین کانال مورد نظر باشد
    ";
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "adminJoinChannel");
} elseif ($user['step'] == "adminJoinChannel") {
    $forward_from_chat = $update['message']['forward_from_chat']['id'];
    if ($forward_from_chat == null)
        $forward_from_chat = $text;
    file_put_contents("doc/adminJoinChannel.txt", $forward_from_chat);

    $msg = "✅ تنظیمات با موفقیت انجام شد.";

    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;
} elseif ($text == $adminCarttocart && in_array($user_id, $_adminList)) {
    $get = file_get_contents("doc/adminCarttocart.txt");
    $msg = "متن تنظیم شده در حال حاضر :
$get

برای تغییر لطفا متن جدید را وارد نمایید:
    ";
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "adminCarttocart");
} elseif ($user['step'] == "adminCarttocart") {
    file_put_contents("doc/adminCarttocart.txt", $text);

    $msg = "✅ تنظیمات با موفقیت انجام شد.";

    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;
} elseif ($text == $adminAgency && in_array($user_id, $_adminList)) {
    $get = file_get_contents("doc/adminAgency.txt");
    $msg = "متن تنظیم شده در حال حاضر :
$get

برای تغییر لطفا متن جدید را وارد نمایید:
    ";
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "adminAgency");
} elseif ($user['step'] == "adminAgency") {
    file_put_contents("doc/adminAgency.txt", $text);

    $msg = "✅ تنظیمات با موفقیت انجام شد.";

    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;
} elseif ($text == $adminZarinToken && in_array($user_id, $_adminList)) {
    $get = file_get_contents("doc/adminZarinToken.txt");
    $msg = "مرچنت تنظیم شده در حال حاضر :
$get

برای تغییر لطفا مرچنت جدید را وارد نمایید:
    ";
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "adminZarinToken");
} elseif ($user['step'] == "adminZarinToken") {
    file_put_contents("doc/adminZarinToken.txt", $text);

    $msg = "✅ تنظیمات با موفقیت انجام شد.";

    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;
} elseif ($text == $adminZibalToken && in_array($user_id, $_adminList)) {
    $get = file_get_contents("doc/adminZibalToken.txt");
    $msg = "مرچنت تنظیم شده در حال حاضر :
$get

برای تغییر لطفا مرچنت جدید را وارد نمایید:
    ";
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "adminZibalToken");
} elseif ($user['step'] == "adminZibalToken") {
    file_put_contents("doc/adminZibalToken.txt", $text);

    $msg = "✅ تنظیمات با موفقیت انجام شد.";

    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;
} elseif ($text == $adminZarinOnOff && in_array($user_id, $_adminList)) {
    $text = file_get_contents("doc/adminZarinOnOff.txt") == "on" ? "off" : "on";
    file_put_contents("doc/adminZarinOnOff.txt", $text);

    $msg = "✅ تنظیمات با موفقیت انجام شد.";

    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;
} elseif ($text == $adminZibalOnOff && in_array($user_id, $_adminList)) {
    $text = file_get_contents("doc/adminZibalOnOff.txt") == "on" ? "off" : "on";
    file_put_contents("doc/adminZibalOnOff.txt", $text);

    $msg = "✅ تنظیمات با موفقیت انجام شد.";

    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;
} elseif ($text == $adminBotStatus && in_array($user_id, $_adminList)) {
    $text = file_get_contents("doc/adminBotStatus.txt") == "on" ? "off" : "on";
    file_put_contents("doc/adminBotStatus.txt", $text);

    $msg = "✅ تنظیمات با موفقیت انجام شد.";

    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;
} elseif ($text == $adminStartText && in_array($user_id, $_adminList)) {
    $get = file_get_contents("doc/adminStartText.txt");
    $msg = "متن تنظیم شده در حال حاضر :
$get

برای تغییر لطفا متن جدید را وارد نمایید:
    ";
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "adminStartText");
} elseif ($user['step'] == "adminStartText") {
    file_put_contents("doc/adminStartText.txt", $text);

    $msg = "✅ تنظیمات با موفقیت انجام شد.";

    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;
} elseif ($text == $adminSupport && in_array($user_id, $_adminList)) {
    $get = file_get_contents("doc/adminSupport.txt");
    $msg = "متن تنظیم شده در حال حاضر :
$get

برای تغییر لطفا متن جدید را وارد نمایید:
    ";
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "adminSupport");
} elseif ($user['step'] == "adminSupport") {
    file_put_contents("doc/adminSupport.txt", $text);

    $msg = "✅ تنظیمات با موفقیت انجام شد.";

    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;
} elseif ($text == $adminAfterOrderText && in_array($user_id, $_adminList)) {
    $get = file_get_contents("doc/adminAfterOrderText.txt");
    $msg = "متن تنظیم شده در حال حاضر :
$get

برای تغییر لطفا متن جدید را وارد نمایید:
    ";
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "adminAfterOrderText");
} elseif ($user['step'] == "adminAfterOrderText") {
    file_put_contents("doc/adminAfterOrderText.txt", $text);

    $msg = "✅ تنظیمات با موفقیت انجام شد.";

    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;
} elseif ($text == $adminAfterPayText && in_array($user_id, $_adminList)) {
    $get = file_get_contents("doc/adminAfterPayText.txt");
    $msg = "متن تنظیم شده در حال حاضر :
$get

برای تغییر لطفا متن جدید را وارد نمایید:
    ";
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "adminAfterPayText");
} elseif ($user['step'] == "adminAfterPayText") {
    file_put_contents("doc/adminAfterPayText.txt", $text);

    $msg = "✅ تنظیمات با موفقیت انجام شد.";

    message($user_id, $msg, $adminMarkup);
    setStep($user_id, "home");
    die;
} elseif ($text == $adminAddFromAPI && in_array($user_id, $_adminList)) {
    mysqli_query($db, "DELETE FROM apis WHERE site IS NULL");
    $apis = mysqli_query($db, "SELECT * FROM apis");
    $msg = "یکی از گزینه های زیر را انتخاب کنید:";

    //>> کیبورد محصولات
    $markup = array('keyboard' => array(), 'resize_keyboard' => true);
    while ($pro = mysqli_fetch_assoc($apis)) {
        $name = $pro['name'];
        $markup['keyboard'][] = array("$name");
    }
    $markup['keyboard'][] = array($adminBack);
    $markup = json_encode($markup);
    //>> نمایش به کاربر
    message($user_id, $msg, $markup);
    setStep($user_id, 'adminAddFromAPI');
    die;
} elseif ($user['step'] == "adminAddFromAPI") {

    $api = mysqli_query($db, "SELECT * FROM apis WHERE name = '$text'");
    if (mysqli_num_rows($api) < 1) {
        $msg = "وبسرویسی با این نام در دیتابیس موجود نیست!

لطفا دوباره تلاش کنید:";

        $apis = mysqli_query($db, "SELECT * FROM apis");

        //>> کیبورد محصولات
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($pro = mysqli_fetch_assoc($apis)) {
            $name = $pro['name'];
            $markup['keyboard'][] = array("$name");
        }
        $markup['keyboard'][] = array($adminBack);
        $markup = json_encode($markup);

        message($user_id, $msg, $markup);
        die;
    }

    $api = mysqli_fetch_assoc($api);
    $idsh = $api['id'];

    mysqli_query($db, "UPDATE user SET datash='$idsh' WHERE uid='$user_id' LIMIT 1");

    $msg = "شما در حال اضافه کردن محصول از وبسرویس $text هستید
لطفا
    
نام محصول
کد محصول
قیمت محصول

را به صورت جداگانه در 3 خط وارد کنید

برای مثال

ممبر تلگرام
35
45000";
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "adminAddFromAPIData");
} elseif ($user['step'] == "adminAddFromAPIData") {
    $explode = explode("\n", $text);
    $name = $explode[0];
    $code = $explode[1];
    $price = $explode[2] / 1000;
    $apiid = $user['datash'];

    if ($name == null || $code == null || $price == null) {
        $msg = "فرمت اطلاعات وارد شده صحیح نیست.
 
لطفا
    
نام محصول
کد محصول
قیمت محصول

را به صورت جداگانه در 3 خط وارد کنید

برای مثال

ممبر تلگرام
35
45000";
        message($user_id, $msg, $adminBackMarkup);
        setStep($user_id, "adminAddFromAPIData");
        die;
    }

    $api = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM apis WHERE id = $apiid"));

    $token = $api['token'];
    $site = $api['site'];
    $apiname = $api['name'];

    message($user_id, "در حال بررسی وبسرویس");

    $data = array(
        'key' => $token,
        // (دریافتی از آذر سوشال (اجباری برای همه عملیات Api      
        'action' => 'services', // (عملیات (فقط برای ثبت سفارش اختیاری برای سایر عملیات اجباری
    );
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $site);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    $services = json_decode($result, true);
    // file_put_contents('results.txt', date("Y/n/d G:i:s").PHP_EOL.$result.PHP_EOL.PHP_EOL,FILE_APPEND);
    curl_close($ch);

    $found = 0;
    $i = 0;
    while ($serviceParams = $services[$i]) {
        $service = $serviceParams['service'];
        if ($code == $service) {
            $found = 1;
            $category = $serviceParams['category'];
            $min = $serviceParams['min'];
            $max = $serviceParams['max'];
            $type = $serviceParams['type'];
            $desc = $serviceParams['desc'];
            $dripfeed = $serviceParams['dripfeed'];
        } else {
            $i++;
            continue;
        }

        //>> ثبت در دیتابیس
        $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM product WHERE api = '$apiid' & code = '$service'"));

        //>> اگر نبود
        if ($num == 0) {

            $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM category WHERE name='$category'"));
            if ($num < 1) {
                mysqli_query($db, "INSERT INTO category(name,api,parent,checked) VALUES('$category',1,'0',1)");
                test("INSERT INTO category(name,api,parent,checked) VALUES('$category',1,'0',1)\n");
            }

            mysqli_query($db, "INSERT INTO product(name,category,price,min,max,des,api,code,checked,old_price) VALUES('$name','$category','$price','$min','$max','$desc','$apiid','$service',1,'$price')");
            test("INSERT INTO product(name,category,price,min,max,des,api,code,checked,old_price) VALUES('$name','$category','$price','$min','$max','$desc','$apiid','$service',1,'$price')");
        } else {
            mysqli_query($db, "UPDATE product SET price='$price' , old_price='$price' , min='$min' , max='$max' , des='$desc' , category = '$category' , checked=1 WHERE api='$apiid' && code='$service'");
            test("UPDATE product SET price='$price' , old_price='$price' , min='$min' , max='$max' , des='$desc' , category = '$category' , checked=1 WHERE api='$apiid' && code='$service'");
        }
        $i++;
    }

    if ($found == 1) {
        $msg = "محصول مورد نظر به دیتابیس اضافه شد.

برای اضافه کردن محصول بعدی به وبسرویس $apiname لطفا اطلاعات محصول را به صورت زیر وارد کنید:
  
نام محصول
کد محصول
قیمت محصول

برای مثال

ممبر تلگرام
35
45000";
    } else {
        $msg = "محصول با کد $code در وبسرویس $apiname یافت نشد.

لطفا دوباره تلاش کنید:";
    }
    message($user_id, $msg, $adminBackMarkup);
    setStep($user_id, "adminAddFromAPIData");
    die;
} elseif ($text == $adminAdmin && in_array($user_id, $_adminList)) {
    $msg = "لیست ادمین ها:

";
    foreach ($_adminList as $admin) {
        $msg .= "<code>$admin</code>
";
    }
    $backMarkup = urlencode(json_encode(array('keyboard' => array(array("حذف ادمین", "افزودن ادمین"), array($adminBack)), 'resize_keyboard' => true)));
    message($user_id, $msg, $backMarkup);
} elseif ($text == "حذف ادمین") {
    $msg = "برای حذف ادمین لطفا چت آی دی ادمین را ارسال نمایید:";
    message($user_id, $msg, $backMarkup);
    setStep($user_id, "DelAdmin");
} elseif ($text == "افزودن ادمین") {
    $msg = "برای افزودن ادمین لطفا چت آی دی ادمین را ارسال نمایید:";
    message($user_id, $msg, $backMarkup);
    setStep($user_id, "AddAdmin");
} elseif ($user['step'] == "DelAdmin") {
    $key = array_search($text, $_adminList);

    // delete the element if found
    if ($key !== false) {
        unset($_adminList[$key]);
        $_adminList = array_unique($_adminList);
        file_put_contents("doc/admins.txt", implode("_", $_adminList));
        $msg = "ادمین با موفقیت از لیست حذف شد!";
        message($user_id, $msg, $adminMarkup);
        setStep($user_id, "home");
    } else {
        $msg = "ادمین مورد نظر یافت نشد!

دوباره تلاش کنید:";
        message($user_id, $msg, $adminBackMarkup);
        die;
    }
} elseif ($user['step'] == "AddAdmin") {
    $key = array_search($text, $_adminList);

    // delete the element if found
    if ($key == null) {
        $_adminList[] = $text;
        $_adminList = array_unique($_adminList);
        file_put_contents("doc/admins.txt", implode("_", $_adminList));
        $msg = "ادمین با موفقیت به لیست اضافه شد!";
        message($user_id, $msg, $adminMarkup);
        setStep($user_id, "home");
    } else {
        $msg = "ادمین در حال حاضر در لیست وجود دارد.

دوباره تلاش کنید:";
        message($user_id, $msg, $adminBackMarkup);
        die;
    }
}

//<<----------------- Hot Keys ----------------->>\\
elseif (strpos($text, '/order') !== false) {
    $text = explode('_', $text);
    $proID = $text[1];
    $quantity = $text[2];
    setStep($user_id, 'none');
    //>> بررسی پارامتر ها
    if ($proID == null and $quantity == null) {
        $msg = "📃 برای ثبت سفارش با استفاده از این دستور به یکی از دو صورت زیر اقدام کنید :


1️⃣ /order_[ProductCode]

در این حالت بعد از خط تیره می توانید کد محصول دلخواه خود را تایپ کنید و با ارسال آن این محصول در سبد خرید شما قرار گرفته و برای ادامه خرید به مرحله وارد کردن تعداد درخواستی هدایت می شوید.

برای مثال برای ثبت سفارش محصولی که کد آن 156 میباشد می باید از دستور پایین استفاده کنید :
/order_156

2️⃣ /order_[ProductCode]_[Number]

در این حالت میتوانید بعد از کد محصول خط تیره و سپس تعداد دلخواهتان را وارد کنید. با وارد کردن این عبارت شما به مرحله آخر ثبت سفارش، یعنی ارسال لینک سفارش هدایت می شوید و میتوانید با وارد کردن لینک، سفارش خود را نهایی کنید.

برای این کار توجه داشته باشید که مقدار وارد شده باید در بازه کمترین تا بیشترین مقدار قابل قبول برای آن محصول قرار داشته باشد و همچنین موجودی کیف پول شما نیز برای ثبت سفارش کافی باشد.

برای مثال برای ثبت سفارش 1500 عدد از محصولی که کد آن 2005 میباشد می باید از دستور پایین استفاده کنید :
/order_2005_1500

⏸ برای جلوگیری از ثبت سفارش ناخواسته و سهوی و همچنین وجود محدودیت های کاراکتری امکان ثبت لینک به عنوان پارامتر سوم در این روش وجود ندارد و حتی اگر بعد از خط تیره سوم لینکی را برای ربات تایپ و ارسال کنید باز هم ربات آن را نادیده گرفته و از شما مجددا لینک را درخواست می کند.

♈️ @$_botUsername";
        message($user_id, $msg, $mainMarkup);
        die;
    }
    //>> به تعداد
    if ($proID !== null and $quantity == null) {
        //>> بررسی ورودی
        $pro = mysqli_query($db, "SELECT * FROM product WHERE id='$proID' AND status = 1 LIMIT 1");
        if (mysqli_num_rows($pro) == 0) {
            $msg = "❌ در حال حاضر محصولی با کد $proID در ربات وجود ندارد.

لطفا دوباره تلاش کنید و یا برای مشاهده آموزش استفاده از این دستور میانبر، متن پایین را برای ربات ارسال کنید :

/order

♈️ @$_botUsername";
            message($user_id, $msg);
            die;
        }
        //>> دیتای کاربر
        $pro = mysqli_fetch_assoc($pro);
        $text = $pro['name'];
        mysqli_query($db, "UPDATE user SET datash='$text' WHERE uid='$user_id' LIMIT 1");
        //>> مشخصات محصول
        $name = $text;
        $max = $pro['max'];
        $min = $pro['min'];
        $price = $pro['price'];
        $api = $pro['api'];
        $id = $pro['id'];
        $apiTell = $api != null ? "
<b>▫️ سرور: </b>$api" : null;

        //>> بررسی قیمت دستی
        $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$id'"));
        if ($num > 0) {
            $price = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$id'"))['cost'] * 1000;
        }
        $description = $pro['des'];
        $msg = "<b>▫️ نام محصول انتخابی: </b>$name
<b>▫️ قیمت:</b> $price<b> تومان برای 1000 عدد


▫️ توضیحات محصول:</b>
$description
.
";
        message($user_id, $msg);
        $msg = "<b>⏪ جهت ثبت سفارش؛ تعداد مورد نظر خود (بین </b>$min<b> تا</b> $max<b> عدد) را وارد نمایید:</b>";
        message($user_id, $msg);
        setStep($user_id, 'orderQuantity');
        die;
    }
    //>> به لینک
    if ($proID !== null and $quantity !== null) {
        $text = $quantity;
        //>> بررسی کد محصول
        $pro = mysqli_query($db, "SELECT * FROM product WHERE id='$proID' AND status = 1 LIMIT 1");
        if (mysqli_num_rows($pro) == 0) {
            $msg = "❌ در حال حاضر محصولی با کد $proID در ربات وجود ندارد.

لطفا متن را ادیت و دوباره تلاش کنید و یا برای مشاهده آموزش استفاده از این دستور میانبر، متن پایین را برای ربات ارسال کنید :

/order

♈️ @$_botUsername";
            message($user_id, $msg);
            die;
        }
        //>> بررسی عدد بودن
        if (!(is_numeric($text))) {
            $msg = "❌ مقدار ورودی صحیح نیست.

لطفا متن را ادیت و دوباره تلاش کنید و یا برای مشاهده آموزش استفاده از این دستور میانبر، متن پایین را برای ربات ارسال کنید :

/order

♈️ @$_botUsername";
            message($user_id, $msg);
            die;
        }
        //>> بررسی بازه تعداد
        $pro = mysqli_fetch_assoc($pro);
        $max = $pro['max'];
        $min = $pro['min'];
        $name = $pro['name'];
        $description = $pro['des'];
        $api = $pro['api'];
        $id = $pro['id'];
        $apiTell = $api != null ? "
<b>▫️ سرور: </b>$api" : null;

        if ($text < $min or $text > $max) {
            $msg = "❌ مقدار ورودی صحیح نیست.

برای این محصول تعداد سفارش باید عددی بین $min تا $max باشد.

لطفا متن را ادیت و دوباره تلاش کنید و یا برای مشاهده آموزش استفاده از این دستور میانبر، متن پایین را برای ربات ارسال کنید :

/order

♈️ @$_botUsername";
            message($user_id, $msg);
            die;
        }
        //>> بررسی موجودی کیف پول
        $price = $pro['price'];
        //>> بررسی قیمت دستی
        $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$pid'"));
        if ($num > 0) {
            $price = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$pid'"))['cost'];
        }
        $hezar = $max > 1000 ? 1000 : $max;
        $hezarPrice = $price * 1000;
        $cost = $price * $text;
        $wallet = $user['wallet'];
        if ($wallet < $cost) {
            $gotowallet = $cost - $wallet;
            $maxAvb = floor($wallet / $pro['price']);

            $msg = "❌ موجودی شما برای ثبت این سفارش کم است.

🌀 موجودی شما $wallet تومان 
💸 هزینه این سفارش $cost تومان 

➕ برای افزایش اعتبار به میزان $gotowallet تومان (شارژ اضافه ی حداقل برای ثبت این سفارش) میتوانید از دستور پایین استفاده کنید:

/charge_$gotowallet

🛂 همچنین میتوانید با توجه به موجودی کیف پولتان حداکثر تعداد $maxAvb عدد و یا مقدار کمتری از این محصول را سفارش دهید:";
            message($user_id, $msg);
            die;
        }

        //>> دیتای کاربر
        mysqli_query($db, "UPDATE user SET datash='$name^$text' WHERE uid='$user_id'");

        //>> لینک سفارش
        $msg = "<b>▫️ نام محصول انتخابی: </b>$name
<b>▫ تعداد: </b>$text<b> عدد
▫ هزینه سفارش: </b>$cost<b> تومان



▫ توضیحات محصول:</b>
$description
.
";

        $markup = json_encode(array('keyboard' => array(array($quantityChange), array($back)), 'resize_keyboard' => true));
        message($user_id, $msg);
        $msg = "<b>↙️ جهت تکمیل لطفا لینک سفارش را برای ربات ارسال نمایید:</b>";
        message($user_id, $msg, $markup);
        setStep($user_id, 'orderVerify');
        die;
    }
} elseif (strpos($text, '/charge') !== false) {
    setStep($user_id, 'none');

    //>> احراز
    if ($user['status'] < 1) {
        $msg = "📲 برای امنیت بیشتر تراکنش ها لازم است که پیش از اولین پرداخت، شماره تماس خود را تایید کنید.

❗️ شماره تماس شما نزد ما محفوظ است و هیچکس امکان دسترسی به آن را ندارد.

یکی از گزینه های زیر را انتخاب کنید:";
        message($user_id, $msg, $phoneMarkup);
        setStep($user_id, "phoneSend");
        die;
    }



    $text = explode('_', $text);
    $charge = $text[1];
    //>> بررسی پارامتر ها
    if ($charge == null) {
        $msg = "📃 برای افزایش موجودی با استفاده از این دستور به صورت زیر عمل کنید:

⏺ /charge_[Amount]

بعد از خط تیره مقدار دلخواه خود را به تومان تایپ و برای ربات ارسال کنید تا لینک پرداخت برای شما به نمایش در بیاید.

برای مثال برای شارژ موجودی ربات به مبلغ 15 هزار تومان متن پایین را برای ربات ارسال نمایید:

/charge_15000

♈️ @$_botUsername";
        message($user_id, $msg, $mainMarkup);
        die;
    }
    //>> بررسی عدد بودن
    if (!(is_numeric($charge))) {
        $msg = "❌ مقدار ورودی صحیح نیست.

لطفا متن را ادیت و دوباره تلاش کنید و یا برای مشاهده آموزش استفاده از این دستور میانبر، متن پایین را برای ربات ارسال کنید :

/charge

♈️ @$_botUsername";
        message($user_id, $msg);
        die;
    }
    //>> شارژ کاربر
    $text = $charge;
    if ($text < 1000)
        $text = 1000;
    if ($charge < 1000)
        $charge = 1000;

    $zarinLink = $_host . "/pay?id=$user_id&amount=$charge";
    $zibalLink = $_host . "/zibal?id=$user_id&amount=$charge";

    $msg = "➕ برای افزایش موجودی حسابتان به اندازه $text بر روی لینک پایین کلیک کنید.

🛂 توجه داشته باشید که پرداخت فاکتور خرید با استفاده از کارت دیگران و یا پرداخت با چند کارت مختلف باعث قفل شدن اکانت شما می شود و تا زمان احراز هویت امکان استفاده از موجودی حساب امکان پذیر نمی باشد.

💳 یکی از درگاه های پایین را برای پرداخت انتخاب کنید:";

    $gatewayStatus = 0;
    $markup = array('inline_keyboard' => []);
    if (file_get_contents("doc/adminZarinOnOff.txt") == "on") {
        $markup['inline_keyboard'][] = [
            [
                'text' => "$zarinButton",
                'url' => urlencode("$zarinLink")
            ]
        ];
        $gatewayStatus = 1;
    }

    if (file_get_contents("doc/adminZibalOnOff.txt") == "on") {
        $markup['inline_keyboard'][] = [
            [
                'text' => "$zibalButton",
                'url' => urlencode("$zibalLink")
            ]
        ];
        $gatewayStatus = 1;
    }

    if ($gatewayStatus == 0) {
        $msg = "در حال حاضر امکان پرداخت آنلاین وجود ندارد. لطفا از روش کارت به کارت برای شارژ حساب خود اقدام نمایید.";
        message($user_id, $msg);
        die;
    }

    message($user_id, $msg, json_encode($markup));
} elseif (strpos($text, '/status') !== false) {
    $track = explode("_", $text)[1];

    $orders = mysqli_query($db, "SELECT * FROM orders WHERE uid='$user_id' AND track='$track' ORDER BY `id` DESC LIMIT 10");
    while ($order = mysqli_fetch_assoc($orders)) {
        $quantity = $order["quantity"];
        $link = $order["link"];
        $cost = $order["cost"];
        $time = $order["time"];
        $time = date("Y/m/d H:i", $time);
        $name = $order["name"];
        $track = $order["track"];
        $status = $order["status"];
        if ($status == 0)
            $status = "در صف ارسال";
        elseif ($status == 1)
            $status = "درحال انجام";
        elseif ($status == 2)
            $status = "کامل شده";
        elseif ($status == 3)
            $status = "کنسل شده";
        elseif ($status == 4)
            $status = "ناتمام";
        $remains = $order['remains'];
        $start_count = $order['start_count'];
        if ($remains == 0)
            $remainsTell = "";
        else
            $remainsTell = "
<b>▪️ باقیمانده:</b> $remains";
        if ($start_count == 0)
            $start_countTell = "";
        else
            $start_countTell = "
<b>▪️ شروع از: </b>$start_count";

        $msg = $msg . "
▫️ $name
<b>▫️ کد پیگیری: </b><code>$track</code>
<b>▫️ لینک: </b>$link
<b>▫️ تعداد: </b>$quantity
<b>▫️ زمان: </b>$time
<b>▫️ هزینه: </b>$cost
<b>🔹️ وضعیت: </b>$status $start_countTell $remainsTell 
"
        ;
    }
    $msg = $msg . ".";
    //>> اطلاع به کاربر
    message($user_id, $msg, $mainMarkup);
    die;
}

//<<----------------- List Keys ----------------->>\\
elseif (strpos($data, 'listButtonxx2_') !== false) {
    //>> استخراج دیتا
    $id = explode("_", $text)[1];
    $text = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM category WHERE id='$id' LIMIT 1"))['name'];
    if ($id == 0)
        $text = 0;
    //>> بررسی مسیر
    $cats = mysqli_query($db, "SELECT * FROM category WHERE parent='$text'");
    //>> ساخت کیبورد کتگوری
    $markup = array('inline_keyboard' => []);
    if (mysqli_num_rows($cats) > 0) {
        while ($cat = mysqli_fetch_assoc($cats)) {
            $catName = $cat['name'];
            $catID = $cat['id'];
            $markup['inline_keyboard'][][] = ['text' => "$catName", 'callback_data' => "listButtonxx2_$catID"];
            ;
        }
        $parent = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM category WHERE id='$id' LIMIT 1"))['parent'];
        $parentID = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM category WHERE name='$parent' LIMIT 1"))['id'];
        if ($parent == 0)
            $parentID = 0;
        if ($text !== 0)
            $markup['inline_keyboard'][][] = ['text' => "$bacCat", 'callback_data' => "listButtonxx2_$parentID"];
        $markup = json_encode($markup);
        //>> ارسال به کاربر
        editMessageReplyMarkup($user_id, $message_id, $markup);
    }
    //>> ساخت کیبورد محصولات
    else {
        $markup['inline_keyboard'][] = [['text' => $priceInlineButton, 'callback_data' => "doneCallbackData"], ['text' => $mahsoolInlineButton, 'callback_data' => "doneCallbackDataMahsool"]];
        $products = mysqli_query($db, "SELECT * FROM product WHERE category='$text' AND status = 1 ORDER BY sort");
        while ($pro = mysqli_fetch_assoc($products)) {
            $name = $pro['name'];
            $price = $pro['price'] * 1000;
            $api = $pro['api'];
            $id = $pro['id'];

            //>> بررسی قیمت دستی
            $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$id'"));
            if ($num > 0) {
                $price = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$id'"))['cost'] * 1000;
                $price = "⭐️ $price";
            }

            $callback = "callbackMahsoolData_$id";
            $markup['inline_keyboard'][] = [['text' => "$price", 'callback_data' => "doneCallbackData"], ['text' => "$name", 'callback_data' => $callback]];
        }
        $parent = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM category WHERE name='$text' LIMIT 1"))['parent'];
        $parentID = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM category WHERE name='$parent' LIMIT 1"))['id'];
        if ($parent === 0)
            $parentID = 0;
        if ($text !== 0)
            $markup['inline_keyboard'][][] = ['text' => "$bacCat", 'callback_data' => "listButtonxx2_$parentID"];
        $markup = json_encode($markup);
        //>> ارسال به کاربر
        editMessageReplyMarkup($user_id, $message_id, $markup);


    }

}
//<<----------------- Auto Order Steps ----------------->>\\
elseif ($user['step'] == 'autoorder') {
    $channel_forward_chat_id = $update['message']['forward_from_chat']['id'];
    $channel_username = $update['message']['forward_from_chat']['username'];
    $channel_title = $update['message']['forward_from_chat']['title'];

    $update1 = json_decode(file_get_contents("https://api.telegram.org/bot" . API_TOKEN . "/getChatMember?chat_id=" . $channel_forward_chat_id . "&user_id=" . $chat_id), true);

    if (!($update1['ok'])) {
        $msg = "
<b>❌ ربات ادمین کانال نیست و یا این کانال قبلا در ربات ثبت شده است!</b>

ربات را ادمین کانال کنید و مجددا یکی از پیام های کانال را برای ربات فوروارد کنید:
";
        message($user_id, $msg);
        die;
    }

    $msg = "کانال $channel_title با موفقیت ثبت شد.

برای ادامه یکی دسته بندی های زیر را انتخاب کنید:    ";

    mysqli_query($db, "UPDATE user SET datam = '$channel_forward_chat_id' WHERE uid='$user_id'");
    //>> ساخت کیبورد کتگوری ها
    $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='0'");
    $markup = array('keyboard' => array(), 'resize_keyboard' => true);
    while ($catName = mysqli_fetch_assoc($cat)['name']) {
        $markup['keyboard'][] = array($catName);
    }
    $markup['keyboard'][] = array($back);
    $markup = json_encode($markup);

    //>> ارسال به کاربر
    message($user_id, $msg, $markup);
    setStep($user_id, 'acategory');

} elseif ($user['step'] == 'acategory') {
    //>> برگشت به کتگوری اصلی
    $name = $user['datash'];
    $parent = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM category WHERE name='$name' LIMIT 1"))['parent'];
    if ($text == $bacCat && $parent == '0') {
        mysqli_query($db, "UPDATE user SET datash='0'  WHERE uid='$user_id'");
        //>> ساخت کیبورد کتگوری ها
        $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='0'");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($catname = mysqli_fetch_assoc($cat)['name']) {
            $markup['keyboard'][] = array($catname);
        }
        $markup['keyboard'][] = array($back);
        $markup = json_encode($markup);
        //>> ارسال به کاربر
        $msg = "یکی از گزینه های زیر را انتخاب کنید";
        message($user_id, $msg, $markup);
        setStep($user_id, 'acategory');
        die;
    }
    //>> برگشت
    if ($text == $bacCat && $parent !== '0') {
        $text = $parent;
        //>> ساخت کیبورد کتگوری
        $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='$text'");
        mysqli_query($db, "UPDATE user SET datash='$parent' WHERE uid='$user_id'");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        if (mysqli_num_rows($cat) > 0) {
            while ($catname = mysqli_fetch_assoc($cat)['name']) {
                $markup['keyboard'][] = array($catname);
            }
        }
        $markup['keyboard'][] = array($back, $bacCat);
        $markup = json_encode($markup);
        $msg = "یکی از گزینه های زیر را انتخاب کنید";
        message($user_id, $msg, $markup);
        setStep($user_id, 'acategory');
        die;
    }
    //>> بررسی ورودی
    $cat = mysqli_query($db, "SELECT * FROM category WHERE name='$text' LIMIT 1");
    if (mysqli_num_rows($cat) == 0) {
        $msg = "❌ مقدار ورودی صحیح نیست.
لطفا فقط از گزینه های ربات برای انتخاب محصول استفاده کنید.";
        message($user_id, $msg);
        die;
    }
    //>> ساخت کیبورد کتگوری
    mysqli_query($db, "UPDATE user SET datash='$text' WHERE uid='$user_id'");
    $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='$text'");
    if (mysqli_num_rows($cat) > 0) {
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($catName = mysqli_fetch_assoc($cat)['name']) {
            $markup['keyboard'][] = array($catName);
        }
        $markup['keyboard'][] = array($back, $bacCat);
        $markup = json_encode($markup);
        //>> ارسال به کاربر
        $msg = "⚙️ لطفا یکی از گزینه های پایین را انتخاب کنید:";
        message($user_id, $msg, $markup);
        setStep($user_id, 'acategory');
        die;
    }
    //>> رسیدن به آخرین زیردسته
    $product = mysqli_query($db, "SELECT * FROM product WHERE category='$text' AND status = 1 ORDER BY sort");
    if (mysqli_num_rows($product) > 0) {
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($proName = mysqli_fetch_assoc($product)['name']) {
            $markup['keyboard'][] = array(urlencode($proName));
        }
        $markup['keyboard'][] = array($back, $bacCat);
        $markup = json_encode($markup);
        //>> ارسال به کاربر
        $msg = "🏷 لطفا یکی از محصولات پایین را انتخاب کنید:";
        $msg = "دسته انتخابی : $text
یک گزینه انتخاب کنید :";
        message($user_id, $msg, $markup);
        setStep($user_id, 'aorderChoose');
        die;
    } else {
        $markup = json_encode(array('keyboard' => array(array($back, $bacCat)), 'resize_keyboard' => true));
        $msg = "❎ متاسفانه هنوز محصولی برای این دسته بندی تعریف نشده است.";
        message($user_id, $msg, $markup);
        die;
    }
} elseif ($user['step'] == 'aorderChoose') {
    //>> برگشت به کتگوری اصلی
    $name = $user['datash'];
    $parent = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM category WHERE name='$name' LIMIT 1"))['parent'];
    if ($text == $bacCat && $parent == '0') {
        mysqli_query($db, "UPDATE user SET datash='0'  WHERE uid='$user_id'");
        //>> ساخت کیبورد کتگوری ها
        $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='0'");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($catname = mysqli_fetch_assoc($cat)['name']) {
            $markup['keyboard'][] = array($catname);
        }
        $markup['keyboard'][] = array($back);
        $markup = json_encode($markup);
        //>> ارسال به کاربر
        $msg = "یکی از گزینه های زیر را انتخاب کنید";
        message($user_id, $msg, $markup);
        setStep($user_id, 'acategory');
        die;
    }
    //>> برگشت
    if ($text == $bacCat && $parent !== '0') {
        $text = $parent;
        //>> ساخت کیبورد کتگوری
        $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='$text'");
        mysqli_query($db, "UPDATE user SET datash='$parent'  WHERE uid='$user_id'");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        if (mysqli_num_rows($cat) > 0) {
            while ($catname = mysqli_fetch_assoc($cat)['name']) {
                $markup['keyboard'][] = array($catname);
            }
        }
        $markup['keyboard'][] = array($back, $bacCat);
        $markup = json_encode($markup);
        $msg = "یکی از گزینه های زیر را انتخاب کنید";
        message($user_id, $msg, $markup);
        setStep($user_id, 'acategory');
        die;
    }
    //>> بررسی ورودی
    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM product WHERE name='$text' AND status = 1"));
    if ($num == 0) {
        $msg = "❌ محصول وارد شده صحیح نمی باشد.

لطفا مجددا تلاش کنید:";
        message($user_id, $msg, $backMarkup);
        die;
    }
    $pro = mysqli_query($db, "SELECT * FROM product WHERE name='$text' AND status = 1");
    //>> دیتای کاربر
    mysqli_query($db, "UPDATE user SET datash='$text' WHERE uid='$user_id' LIMIT 1");
    //>> مشخصات محصول
    $pro = mysqli_fetch_assoc($pro);
    $name = $text;
    $pid = $pro['id'];
    $max = $pro['max'];
    $min = $pro['min'];
    $price = $pro['price'];
    $api = $pro['api'];
    $id = $pro['id'];
    $apiTell = $api != null ? "
<b>▫️ سرور: </b>$api" : null;
    //>> بررسی قیمت دستی
    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$pid'"));
    if ($num > 0) {
        $price = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$pid'"))['cost'];
    }
    $hezar = $max > 1000 ? 1000 : $max;
    $hezarPrice = $price * 1000;

    $description = $pro['des'];
    $msg = "<b>▫️ نام محصول انتخابی: </b>$name
<b>▫️ قیمت:</b> $hezarPrice<b> تومان برای 1000 عدد

▫️ توضیحات محصول:</b>
$description
.
";

    message($user_id, $msg, $backMarkup);
    $msg = "<b>⏪ جهت ثبت سفارش؛ تعداد مورد نظر خود (بین </b>$min<b> تا</b> $max<b> عدد) را وارد نمایید:</b>";
    message($user_id, $msg, $backMarkup);
    setStep($user_id, 'aorderQuantity');
    die;
} elseif ($user['step'] == 'aorderQuantity') {
    //>> بررسی عدد بودن
    if (!(is_numeric($text))) {
        $msg = "❌ مقدار ورودی صحیح نیست.
لطفا تعداد مورد نظر خود را با استفاده از اعداد انگلیسی برای ربات ارسال کنید.";
        message($user_id, $msg);
        die;
    }
    //>> بررسی بازه تعداد
    $pro = $user['datash'];
    $pro = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE name='$pro' AND status = 1"));
    $max = $pro['max'];
    $min = $pro['min'];
    $pid = $pro['id'];
    $name = $pro['name'];
    $price = $pro['price'];
    $api = $pro['api'];
    $id = $pro['id'];
    $apiTell = $api != null ? "
<b>▫️ سرور: </b>$api" : null;
    //>> بررسی قیمت دستی
    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$pid'"));
    if ($num > 0) {
        $price = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$pid'"))['cost'];
    }
    $description = $pro['des'];
    if ($text < $min or $text > $max) {
        $msg = "❌ مقدار ورودی صحیح نیست.

برای این محصول تعداد سفارش باید عددی بین $min تا $max باشد.

↙️ لطفا تعداد مورد نظر خود را وارد و یا انتخاب کنید:";
        message($user_id, $msg);
        die;
    }
    //>> بررسی موجودی کیف پول
    $cost = $price * $text;
    $wallet = $user['wallet'];

    //>> دیتای کاربر
    mysqli_query($db, "UPDATE user SET datash=CONCAT(datash,'^$text') WHERE uid='$user_id'");

    //>> لینک سفارش
    $msg = "<b>▫️ نام محصول انتخابی: </b>$name
<b>▫ تعداد: </b>$text<b> عدد
▫ هزینه سفارش: </b>$cost<b> تومان
.</b>
";

    $markup = json_encode(array('keyboard' => array(array($quantityChange), array($back)), 'resize_keyboard' => true));
    message($user_id, $msg);
    $msg = "<b>↙️ جهت تکمیل لطفا یک پیام از کانال مورد نظر برای ربات فوروارد نمایید:</b>";
    message($user_id, $msg, $markup);
    setStep($user_id, 'aorderVerify');
    die;

} elseif ($user['step'] == 'aorderVerify') {
    $status = 0;
    //>> محصول انتخابی کاربر
    $pro = $user['datash'];
    $name = explode('^', $pro)[0];
    $quantity = explode('^', $pro)[1];

    //>> تغییر تعداد
    if ($text == $quantityChange) {
        //>> بررسی ورودی
        $text = $name;
        $pro = mysqli_query($db, "SELECT * FROM product WHERE name='$text' AND status = 1 LIMIT 1");
        if (mysqli_num_rows($pro) == 0) {
            $msg = "❌ مقدار ورودی صحیح نیست.
لطفا فقط از گزینه های ربات برای انتخاب محصول استفاده کنید.";
            message($user_id, $msg);
            die;
        }

        //>> دیتای کاربر
        mysqli_query($db, "UPDATE user SET datash='$text' WHERE uid='$user_id' LIMIT 1");
        //>> مشخصات محصول
        $pro = mysqli_fetch_assoc($pro);
        $name = $text;
        $pid = $pro['id'];
        $max = $pro['max'];
        $min = $pro['min'];
        $price = $pro['price'];
        $api = $pro['api'];
        $id = $pro['id'];
        $apiTell = $api != null ? "
<b>▫️ سرور: </b>$api" : null;
        //>> بررسی قیمت دستی
        $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$pid'"));
        if ($num > 0) {
            $price = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$pid'"))['cost'];
        }
        $hezar = $max > 1000 ? 1000 : $max;
        $hezarPrice = $price * 1000;

        $description = $pro['des'];
        $msg = "<b>▫️ نام محصول انتخابی: </b>$name
<b>▫️ قیمت:</b> $hezarPrice<b> تومان برای 1000 عدد

▫ توضیحات محصول:</b>
$description
.
";

        message($user_id, $msg, $backMarkup);
        $msg = "<b>⏪ جهت ثبت سفارش؛ تعداد مورد نظر خود (بین </b>$min<b> تا</b> $max<b> عدد) را وارد نمایید:</b>";
        message($user_id, $msg, $backMarkup);
        setStep($user_id, 'aorderQuantity');
        die;
    }

    $channel_forward_chat_id = $update['message']['forward_from_chat']['id'];
    $channel_username = $update['message']['forward_from_chat']['username'];
    $channel_title = $update['message']['forward_from_chat']['title'];

    $update1 = json_decode(file_get_contents("https://api.telegram.org/bot" . API_TOKEN . "/getChatMember?chat_id=" . $channel_forward_chat_id . "&user_id=" . $chat_id), true);

    if (!($update1['ok'])) {
        $msg = "
<b>❌ ربات ادمین کانال نیست و یا این کانال قبلا در ربات ثبت شده است!</b>

ربات را ادمین کانال کنید و مجددا یکی از پیام های کانال را برای ربات فوروارد کنید:
";
        message($user_id, $msg);
        die;
    }


    //>> دیتای کاربر
    mysqli_query($db, "UPDATE user SET datash='$name' WHERE uid='$user_id' LIMIT 1");
    $pro = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE name='$name' AND status = 1"));
    $description = $pro['des'];
    $code = $pro['id'];
    $id = $code;
    $papi = $pro['api'];
    $apicode = $pro['code'];
    $doChannel = $pro['doChannel'];
    $price = $pro['price'];

    //>> بررسی قیمت دستی
    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$pid'"));
    if ($num > 0) {
        $price = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$pid'"))['cost'];
    }
    //>> بررسی موجودی کیف پول
    $cost = $price * $quantity;
    $wallet = $user['wallet'];
    //>> ثبت سفارش
    $time = time();
    $timeTell = date("Y/m/d", $time);
    $peygiri = substr(str_shuffle("1234567890qwertyuiopasdfghjklzxcvbnm"), 4, 10);

    mysqli_query($db, "INSERT INTO autoorders(uid,pid,quantity,cid) VALUES('$user_id','$code','$quantity','$channel_forward_chat_id')");

    $inserted_id = mysqli_insert_id($db);

    //>> اطلاع به کاربر
    $msg = "<b>✅ سفارش خودکار شما با شناسه با موفقیت ثبت شد.

▫ نام محصول انتخابی: </b>$name
<b>▫ تعداد: </b>$quantity<b> عدد
▫ کانال سفارش : </b> $channel_title $channel_forward_chat_id<b>
❌ لغو: </b> /cancel_$inserted_id<b>

💵 هزینه سفارش: </b>$cost<b> تومان
💰 موجودی شما: </b>$wallet <b>تومان
</b>
♈️ @$_botUsername
";
    message($user_id, $msg);

    $get = file_get_contents("doc/adminAfterOrderText.txt");
    message($user_id, $get, $mainMarkup);

    //>> کانال گزارش

    $msg = "🔔 #سفارش خودکار
👤 <a href='tg://user?id=" . $user_id . "'>" . $user_id . "</a>
🛒 نام: $name
🛍 تعداد: $quantity عدد
🔗 کانال: $text
";

    message($_apiOrderReport, $msg);
    $msgid = 0;


    //>> تغییرات دیتابیس
    $msg = "⚙️ لطفا یکی از گزینه های زیر را انتخاب کنید :";
    message($user_id, $msg, $mainMarkup);
    setStep($user_id, "none");

    die;

} elseif (strpos($text, 'cancel_') !== false) {
    $id = explode("_", $text)[1];

    $order = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM autoorders WHERE id = '$id' AND uid = '$user_id'"));

    if (!($order['uid'] == $user_id)) {
        $msg = "سفارشی با این مشخصات یافت نشد!";
        message($user_id, $msg);
        die;
    }

    mysqli_query($db, "DELETE FROM autoorders WHERE id = '$id' AND uid = '$user_id'");
    $msg = "سفارش خودکار شما با موفقیت حذف شد!";
    message($user_id, $msg, $mainMarkup);
    setStep($user_id, "home");
}
//<<----------------- Order Step ----------------->>\\
elseif ($user['step'] == 'products') {
    //>> بررسی ورودی
    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM category WHERE name='$text'"));
    if ($num == 0) {
        $msg = "❌ دسته بندی وارد شده صحیح نمی باشد.

لطفا مجددا تلاش کنید:";
        message($user_id, $msg, $backMarkup);
        die;
    }
    //>> ساخت کیبورد محصول ها
    $cat = mysqli_query($db, "SELECT * FROM product WHERE category='$text' AND status = 1 ORDER BY sort");
    $markup = array('keyboard' => array(), 'resize_keyboard' => true);
    while ($catName = mysqli_fetch_assoc($cat)['name']) {
        $markup['keyboard'][] = array($catName);
    }
    $markup['keyboard'][] = array($back);
    $markup = json_encode($markup);

    //>> ارسال به کاربر
    $msg = "⚙️ لطفا یکی از گزینه های پایین را انتخاب کنید:";
    message($user_id, $msg, $markup);
    setStep($user_id, 'orderChoose');
} elseif ($user['step'] == 'ocategory') {
    //>> برگشت به کتگوری اصلی
    $name = $user['datash'];
    $parent = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM category WHERE name='$name' LIMIT 1"))['parent'];
    if ($text == $bacCat && $parent == '0') {
        mysqli_query($db, "UPDATE user SET datash='0'  WHERE uid='$user_id'");
        //>> ساخت کیبورد کتگوری ها
        $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='0'");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($catname = mysqli_fetch_assoc($cat)['name']) {
            $markup['keyboard'][] = array($catname);
        }
        $markup['keyboard'][] = array($back);
        $markup = json_encode($markup);
        //>> ارسال به کاربر
        $msg = "یکی از گزینه های زیر را انتخاب کنید";
        message($user_id, $msg, $markup);
        setStep($user_id, 'ocategory');
        die;
    }
    //>> برگشت
    if ($text == $bacCat && $parent !== '0') {
        $text = $parent;
        //>> ساخت کیبورد کتگوری
        $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='$text'");
        mysqli_query($db, "UPDATE user SET datash='$parent' WHERE uid='$user_id'");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        if (mysqli_num_rows($cat) > 0) {
            while ($catname = mysqli_fetch_assoc($cat)['name']) {
                $markup['keyboard'][] = array($catname);
            }
        }
        $markup['keyboard'][] = array($back, $bacCat);
        $markup = json_encode($markup);
        $msg = "یکی از گزینه های زیر را انتخاب کنید";
        message($user_id, $msg, $markup);
        setStep($user_id, 'ocategory');
        die;
    }
    //>> بررسی ورودی
    $cat = mysqli_query($db, "SELECT * FROM category WHERE name='$text' LIMIT 1");
    if (mysqli_num_rows($cat) == 0) {
        $msg = "❌ مقدار ورودی صحیح نیست.
لطفا فقط از گزینه های ربات برای انتخاب محصول استفاده کنید.";
        message($user_id, $msg);
        die;
    }
    //>> ساخت کیبورد کتگوری
    mysqli_query($db, "UPDATE user SET datash='$text' WHERE uid='$user_id'");
    $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='$text'");
    if (mysqli_num_rows($cat) > 0) {
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($catName = mysqli_fetch_assoc($cat)['name']) {
            $markup['keyboard'][] = array($catName);
        }
        $markup['keyboard'][] = array($back, $bacCat);
        $markup = json_encode($markup);
        //>> ارسال به کاربر
        $msg = "⚙️ لطفا یکی از گزینه های پایین را انتخاب کنید:";
        message($user_id, $msg, $markup);
        setStep($user_id, 'ocategory');
        die;
    }
    //>> رسیدن به آخرین زیردسته
    $product = mysqli_query($db, "SELECT * FROM product WHERE category='$text' AND status = 1 ORDER BY sort");
    if (mysqli_num_rows($product) > 0) {
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($proName = mysqli_fetch_assoc($product)['name']) {
            $markup['keyboard'][] = array(urlencode($proName));
        }
        $markup['keyboard'][] = array($back, $bacCat);
        $markup = json_encode($markup);
        //>> ارسال به کاربر
        $msg = "🏷 لطفا یکی از محصولات پایین را انتخاب کنید:";
        $msg = "دسته انتخابی : $text
یک گزینه انتخاب کنید :";
        message($user_id, $msg, $markup);
        setStep($user_id, 'orderChoose');
        die;
    } else {
        $markup = json_encode(array('keyboard' => array(array($back, $bacCat)), 'resize_keyboard' => true));
        $msg = "❎ متاسفانه هنوز محصولی برای این دسته بندی تعریف نشده است.";
        message($user_id, $msg, $markup);
        die;
    }
} elseif ($user['step'] == 'orderChoose') {
    //>> برگشت به کتگوری اصلی
    $name = $user['datash'];
    $parent = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM category WHERE name='$name' LIMIT 1"))['parent'];
    if ($text == $bacCat && $parent == '0') {
        mysqli_query($db, "UPDATE user SET datash='0'  WHERE uid='$user_id'");
        //>> ساخت کیبورد کتگوری ها
        $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='0'");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        while ($catname = mysqli_fetch_assoc($cat)['name']) {
            $markup['keyboard'][] = array($catname);
        }
        $markup['keyboard'][] = array($back);
        $markup = json_encode($markup);
        //>> ارسال به کاربر
        $msg = "یکی از گزینه های زیر را انتخاب کنید";
        message($user_id, $msg, $markup);
        setStep($user_id, 'ocategory');
        die;
    }
    //>> برگشت
    if ($text == $bacCat && $parent !== '0') {
        $text = $parent;
        //>> ساخت کیبورد کتگوری
        $cat = mysqli_query($db, "SELECT * FROM category WHERE parent='$text'");
        mysqli_query($db, "UPDATE user SET datash='$parent'  WHERE uid='$user_id'");
        $markup = array('keyboard' => array(), 'resize_keyboard' => true);
        if (mysqli_num_rows($cat) > 0) {
            while ($catname = mysqli_fetch_assoc($cat)['name']) {
                $markup['keyboard'][] = array($catname);
            }
        }
        $markup['keyboard'][] = array($back, $bacCat);
        $markup = json_encode($markup);
        $msg = "یکی از گزینه های زیر را انتخاب کنید";
        message($user_id, $msg, $markup);
        setStep($user_id, 'ocategory');
        die;
    }
    //>> بررسی ورودی
    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM product WHERE name='$text' AND status = 1"));
    if ($num == 0) {
        $msg = "❌ محصول وارد شده صحیح نمی باشد.

لطفا مجددا تلاش کنید:";
        message($user_id, $msg, $backMarkup);
        die;
    }
    $pro = mysqli_query($db, "SELECT * FROM product WHERE name='$text' AND status = 1");
    //>> دیتای کاربر
    mysqli_query($db, "UPDATE user SET datash='$text' WHERE uid='$user_id' LIMIT 1");
    //>> مشخصات محصول
    $pro = mysqli_fetch_assoc($pro);
    $name = $text;
    $pid = $pro['id'];
    $max = $pro['max'];
    $min = $pro['min'];
    $price = $pro['price'];
    $api = $pro['api'];
    $id = $pro['id'];
    $apiTell = $api != null ? "
<b>▫️ سرور: </b>$api" : null;
    //>> بررسی قیمت دستی
    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$pid'"));
    if ($num > 0) {
        $price = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$pid'"))['cost'];
    }
    $hezar = $max > 1000 ? 1000 : $max;
    $hezarPrice = $price * 1000;

    $description = $pro['des'];
    $msg = "<b>▫️ نام محصول انتخابی: </b>$name
<b>▫️ قیمت:</b> $hezarPrice<b> تومان برای 1000 عدد

▫️ توضیحات محصول:</b>
$description
.
";

    message($user_id, $msg, $backMarkup);
    $msg = "<b>⏪ جهت ثبت سفارش؛ تعداد مورد نظر خود (بین </b>$min<b> تا</b> $max<b> عدد) را وارد نمایید:</b>";
    message($user_id, $msg, $backMarkup);
    setStep($user_id, 'orderQuantity');
    die;
} elseif ($user['step'] == 'orderQuantity') {
    //>> بررسی عدد بودن
    if (!(is_numeric($text))) {
        $msg = "❌ مقدار ورودی صحیح نیست.
لطفا تعداد مورد نظر خود را با استفاده از اعداد انگلیسی برای ربات ارسال کنید.";
        message($user_id, $msg);
        die;
    }
    //>> بررسی بازه تعداد
    $pro = $user['datash'];
    $pro = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE name='$pro' AND status = 1"));
    $max = $pro['max'];
    $min = $pro['min'];
    $pid = $pro['id'];
    $name = $pro['name'];
    $price = $pro['price'];
    $api = $pro['api'];
    $id = $pro['id'];
    $apiTell = $api != null ? "
<b>▫️ سرور: </b>$api" : null;
    //>> بررسی قیمت دستی
    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$pid'"));
    if ($num > 0) {
        $price = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$pid'"))['cost'];
    }
    $description = $pro['des'];
    if ($text < $min or $text > $max) {
        $msg = "❌ مقدار ورودی صحیح نیست.

برای این محصول تعداد سفارش باید عددی بین $min تا $max باشد.

↙️ لطفا تعداد مورد نظر خود را وارد و یا انتخاب کنید:";
        message($user_id, $msg);
        die;
    }
    //>> بررسی موجودی کیف پول
    $cost = $price * $text;
    $wallet = $user['wallet'];
    if ($wallet < $cost) {
        $gotowallet = $cost - $wallet;
        $maxAvb = floor($wallet / $price);

        $msg = "❌ موجودی شما برای ثبت این سفارش کم است.

🌀 موجودی شما $wallet تومان 
💸 هزینه این سفارش $cost تومان 

➕ برای افزایش اعتبار به میزان $gotowallet تومان (شارژ اضافه ی حداقل برای ثبت این سفارش) میتوانید از دستور پایین استفاده کنید:

/charge_$gotowallet

🛂 همچنین میتوانید با توجه به موجودی کیف پولتان حداکثر تعداد $maxAvb عدد و یا مقدار کمتری از این محصول را سفارش دهید:";
        message($user_id, $msg);
        die;
    }

    //>> دیتای کاربر
    mysqli_query($db, "UPDATE user SET datash=CONCAT(datash,'^$text') WHERE uid='$user_id'");

    //>> لینک سفارش
    $msg = "<b>▫️ نام محصول انتخابی: </b>$name
<b>▫ تعداد: </b>$text<b> عدد
▫ هزینه سفارش: </b>$cost<b> تومان
.</b>
";

    $markup = json_encode(array('keyboard' => array(array($quantityChange), array($back)), 'resize_keyboard' => true));
    message($user_id, $msg);
    $msg = "<b>↙️ جهت تکمیل لطفا لینک سفارش را برای ربات ارسال نمایید:</b>";
    message($user_id, $msg, $markup);
    setStep($user_id, 'orderVerify');
    die;

} elseif ($user['step'] == 'orderVerify') {
    $status = 0;
    //>> محصول انتخابی کاربر
    $pro = $user['datash'];
    $name = explode('^', $pro)[0];
    $quantity = explode('^', $pro)[1];
    //>> تغییر تعداد
    if ($text == $quantityChange) {
        //>> بررسی ورودی
        $text = $name;
        $pro = mysqli_query($db, "SELECT * FROM product WHERE name='$text' AND status = 1 LIMIT 1");
        if (mysqli_num_rows($pro) == 0) {
            $msg = "❌ مقدار ورودی صحیح نیست.
لطفا فقط از گزینه های ربات برای انتخاب محصول استفاده کنید.";
            message($user_id, $msg);
            die;
        }

        //>> دیتای کاربر
        mysqli_query($db, "UPDATE user SET datash='$text' WHERE uid='$user_id' LIMIT 1");
        //>> مشخصات محصول
        $pro = mysqli_fetch_assoc($pro);
        $name = $text;
        $pid = $pro['id'];
        $max = $pro['max'];
        $min = $pro['min'];
        $price = $pro['price'];
        $api = $pro['api'];
        $id = $pro['id'];
        $apiTell = $api != null ? "
<b>▫️ سرور: </b>$api" : null;
        //>> بررسی قیمت دستی
        $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$pid'"));
        if ($num > 0) {
            $price = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$pid'"))['cost'];
        }
        $hezar = $max > 1000 ? 1000 : $max;
        $hezarPrice = $price * 1000;

        $description = $pro['des'];
        $msg = "<b>▫️ نام محصول انتخابی: </b>$name
<b>▫️ قیمت:</b> $hezarPrice<b> تومان برای 1000 عدد

▫ توضیحات محصول:</b>
$description
.
";

        message($user_id, $msg, $backMarkup);
        $msg = "<b>⏪ جهت ثبت سفارش؛ تعداد مورد نظر خود (بین </b>$min<b> تا</b> $max<b> عدد) را وارد نمایید:</b>";
        message($user_id, $msg, $backMarkup);
        setStep($user_id, 'orderQuantity');
        die;
    }
    //>> دیتای کاربر
    mysqli_query($db, "UPDATE user SET datash='$name' WHERE uid='$user_id' LIMIT 1");
    $pro = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM product WHERE name='$name' AND status = 1"));
    $description = $pro['des'];
    $code = $pro['id'];
    $id = $code;
    $papi = $pro['api'];
    $apicode = $pro['code'];
    $doChannel = $pro['doChannel'];
    $price = $pro['price'];

    //>> بررسی قیمت دستی
    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$code'"));
    if ($num > 0) {
        $price = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM manual WHERE uid='$user_id' and pid='$code'"))['cost'];
    }
    //>> بررسی موجودی کیف پول
    $cost = $price * $quantity;
    $wallet = $user['wallet'];
    if ($wallet < $cost) {
        $gotowallet = $cost - $wallet;
        $maxAvb = floor($wallet / $price);
        $msg = "❌ موجودی شما برای ثبت این سفارش کم است.

🌀 موجودی شما $wallet تومان 
💸 هزینه این سفارش $cost تومان 

➕ برای افزایش اعتبار به میزان $gotowallet تومان (شارژ اضافه ی حداقل برای ثبت این سفارش) میتوانید از دستور پایین استفاده کنید:

/charge_$gotowallet

🛂 همچنین میتوانید با توجه به موجودی کیف پولتان حداکثر تعداد $maxAvb عدد و یا مقدار کمتری از این محصول را سفارش دهید:";
        message($user_id, $msg);
        setStep($user_id, 'orderQuantity');
        die;
    }
    //>> ثبت سفارش
    $time = time();
    $timeTell = date("Y/m/d", $time);
    $wallet = $wallet - $cost;
    $peygiri = substr(str_shuffle("1234567890qwertyuiopasdfghjklzxcvbnm"), 4, 10);

    $peygiri = rand(1000000000, 9999999999);
    while (mysqli_num_rows(mysqli_query($db, "SELECT * FROM orders WHERE track = '$peygiri'")) > 0) {
        $peygiri = rand(1000000000, 9999999999);
    }

    //>> بررسی وبسرویس
    $ordersAPIStatus = 0;

    //--------> API <--------\\
    if ($papi > 0) {
        $ordersAPIStatus = 1;
        $api = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM apis WHERE id = '$papi'"));

        $token = $api['token'];
        $site = $api['site'];
        $apiname = $api['name'];

        $data = array(
            'key' => $token,
            'action' => 'add',
            'link' => $text,
            'quantity' => $quantity,
            'service' => $apicode,
        );
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $site);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $result = curl_exec($ch);
        $peygiri1 = json_decode($result, true);
        $peygiri = $peygiri1['order'];
        curl_close($ch);
        if ($peygiri1['order'] == false) {
            file_put_contents('error.txt', date("Y/n/d G:i:s") . PHP_EOL . $result . PHP_EOL . PHP_EOL, FILE_APPEND);
        }

        //>> بررسی انجام خودکار سفارش
        if ($peygiri == null) {
            //>> اطلاع به کاربر
            $msg = "❌ مشکلی در ثبت سفارش به وجود آمده است!

لطفا چند دقیقه بعد مجددا تلاش کنید و یا به پشتیبانی ربات اطلاع دهید.";
            message($user_id, $msg, $mainMarkup);
            setStep($user_id, "home");
            //>> اطلاع به کانال
            $msg = "❌  #ارور وبسرویس

👤 <a href='tg://user?id=" . $user_id . "'>" . $user_id . "</a>

⚙️ کد وبسرویس : $apiname | $papi | $apicode
$name
🛍 تعداد : $quantity
🔗 لینک : $text

";
            message($_orderReport, $msg);
            message($_orderReport, $result);
            die;
        }

    }

    //>> اطلاع به کاربر
    $msg = "<b>✅ سفارش شما با شناسه <code>$peygiri</code> با موفقیت ثبت شد.

▫ نام محصول انتخابی: </b>$name
<b>▫ تعداد: </b>$quantity<b> عدد
▫ لینک سفارش : </b>$text
<b>🕰 $timeTell
▫ کد پیگیری:</b> <code>$peygiri</code><b>

💵 هزینه سفارش: </b>$cost<b> تومان
💰 موجودی جدید: </b>$wallet <b>تومان
</b>
♈️ @$_botUsername
";
    $markup = json_encode(
        array(
            'inline_keyboard' => array(
                array(array('text' => $reorderstatus, 'callback_data' => "/status_$peygiri")),
            )
        )
    );
    message($user_id, $msg, $markup);

    $get = file_get_contents("doc/adminAfterOrderText.txt");
    message($user_id, $get, $mainMarkup);

    //>> کانال گزارش

    $msg = "🔔 #سفارش جدید
👤 <a href='tg://user?id=" . $user_id . "'>" . $user_id . "</a>
🛒 نام: $name
🛍 تعداد: $quantity عدد
🔗 لینک: $text

🎯 کد پیگیری : <code>$peygiri</code>

💵 هزینه سفارش : $cost تومان
💰 موجودی جدید : $wallet تومان

🕰 $timeTell

";
    if ($ordersAPIStatus == 1) { // سفارشات خودکار
        message($_apiOrderReport, $msg);
        $msgid = 0;
    } else { // سفارشات عادی
        $markup = json_encode(array('inline_keyboard' => array(array(array('text' => $startetela, 'callback_data' => 'begin_' . $peygiri)), array(array('text' => $endetela, 'callback_data' => 'end_' . $peygiri)), array(array('text' => $canceletela, 'callback_data' => 'laghv_' . $peygiri)))));
        $res = message($_orderReport, $msg, $markup);
        $msgid = $res['result']['message_id'] == null ? 0 : $res['result']['message_id'];
    }

    //>> تغییرات دیتابیس
    mysqli_query($db, "INSERT INTO orders(uid,name,quantity,link,cost,time,track,api,msgid,status) VALUES('$user_id','$name','$quantity','$text','$cost','$time','$peygiri','$papi','$msgid','$status')");
    mysqli_query($db, "UPDATE user SET wallet=$wallet WHERE uid='$user_id' LIMIT 1");

    $msg = "⚙️ لطفا یکی از گزینه های زیر را انتخاب کنید :";
    message($user_id, $msg, $mainMarkup);
    setStep($user_id, "none");

    die;

} elseif ($user['step'] == 'orderCheck') {
    //>> بررسی وجود سفارش
    $orderNo = mysqli_num_rows(mysqli_query($db, "SELECT * FROM orders WHERE uid='$user_id' AND track='$text'"));
    if ($orderNo == 0) {
        $msg = "🤷🏻‍♂ سفارشی با این کد پیگیری برای شما ثبت نشده است..";
        message($user_id, $msg);
        die;
    }

    //>> ساخت پیام
    $msg = "اطلاعات سفارش شما:
";
    $orders = mysqli_query($db, "SELECT * FROM orders WHERE uid='$user_id' AND track='$text'");
    $order = mysqli_fetch_assoc($orders);
    $quantity = $order["quantity"];
    $link = $order["link"];
    $cost = $order["cost"];
    $time = $order["time"];
    $time = date("Y/m/d H:i", $time);
    $name = $order["name"];
    $track = $order["track"];
    $status = $order["status"];
    if ($status == 0)
        $status = "در صف ارسال";
    elseif ($status == 1)
        $status = "درحال انجام";
    elseif ($status == 2)
        $status = "کامل شده";
    elseif ($status == 3)
        $status = "کنسل شده";
    elseif ($status == 4)
        $status = "ناتمام";
    $remains = $order['remains'];
    $start_count = $order['start_count'];
    if ($remains == 0)
        $remainsTell = "";
    else
        $remainsTell = "
<b>▪️ باقیمانده:</b> $remains";
    if ($start_count == 0)
        $start_countTell = "";
    else
        $start_countTell = "
<b>▪️ شروع از: </b>$start_count";

    $msg = $msg . "
▫️ $name
<b>▫️ کد پیگیری: </b><code>$track</code>
<b>▫️ لینک: </b>$link
<b>▫️ تعداد: </b>$quantity
<b>▫️ زمان: </b>$time
<b>▫️ هزینه: </b>$cost
<b>🔹️ وضعیت: </b>$status $start_countTell $remainsTell 
";

    //>> اطلاع به کاربر
    message($user_id, $msg, $orderCheckMarkup);
    setStep($user_id, "home");
    die;
}

//<<----------------- Other Steps ----------------->>\\
elseif ($user['step'] == 'plus') {
    //>> بررسی عدد بودن
    if (!(is_numeric($text))) {
        $msg = "❌ مقدار ورودی صحیح نیست.
لطفا شارژ مورد نظر خود را با استفاده از اعداد انگلیسی برای ربات ارسال کنید.";
        message($user_id, $msg);
        die;
    }
    if ($text < 1000 || $text > 5000000) {
        $msg = "❌ حداقل شارژ 1,000 و حداکثر 5,000,000 تومان است
لطفا شارژ مورد نظر خود را با استفاده از اعداد انگلیسی برای ربات ارسال کنید.";
        message($user_id, $msg);
        die;
    }
    //>> ساخت لینک پرداخت
    $msg = "در حال ساخت لینک پرداخت ...";
    message($user_id, $msg, $mainMarkup);

    //>> شارژ کاربر
    if ($text < 1000)
        $text = 1000;
    if ($charge < 1000)
        $charge = 1000;
    $zarinLink = $_host . "/pay?id=$user_id&amount=$text";
    $zibalLink = $_host . "/zibal?id=$user_id&amount=$text";

    $msg = "➕ برای افزایش موجودی حسابتان به اندازه $text بر روی لینک پایین کلیک کنید.

🛂 توجه داشته باشید که پرداخت فاکتور خرید با استفاده از کارت دیگران و یا پرداخت با چند کارت مختلف باعث قفل شدن اکانت شما می شود و تا زمان احراز هویت امکان استفاده از موجودی حساب امکان پذیر نمی باشد.

💳 یکی از درگاه های پایین را برای پرداخت انتخاب کنید:";

    $gatewayStatus = 0;
    $markup = array('inline_keyboard' => []);
    if (file_get_contents("doc/adminZarinOnOff.txt") == "on") {
        $markup['inline_keyboard'][] = [
            [
                'text' => "$zarinButton",
                'url' => urlencode("$zarinLink")
            ]
        ];
        $gatewayStatus = 1;
    }

    if (file_get_contents("doc/adminZibalOnOff.txt") == "on") {
        $markup['inline_keyboard'][] = [
            [
                'text' => "$zibalButton",
                'url' => urlencode("$zibalLink")
            ]
        ];
        $gatewayStatus = 1;
    }

    if ($gatewayStatus == 0) {
        $msg = "در حال حاضر امکان پرداخت آنلاین وجود ندارد. لطفا از روش کارت به کارت برای شارژ حساب خود اقدام نمایید.";
        message($user_id, $msg);
        die;
    }
    $markup = json_encode($markup);
    message($user_id, $msg, $markup);
} elseif ($user['step'] == 'support') {
    //>> اطلاع به کاربر
    $msg = "📨 پیام شما با موفقیت برای تیم پشتیبانی ما ارسال شده است. به زودی پیامتان بررسی و در صورت لزوم به آن پاسخ داده خواهد شد.

با تشکر از همراهی و صبوری شما 🌺

⚙️ لطفا یکی از گزینه های زیر را انتخاب کنید :";
    message($user_id, $msg, $mainMarkup);
    setStep($user_id, 'home');
    //>> ارسال به کانال تیکت
    $id = forward($_ticketReport, $user_id, $message_id)['result']['message_id'];
    $msg = "📩 پیام ارسال شده توسط کاربر
<a href='tg://user?id=" . $user_id . "'>" . $user_id . "</a>";
    $markup = json_encode(array('inline_keyboard' => array(array(array('text' => "$answer", 'url' => "https://t.me/$_botUsername?start=mes$user_id")))));
    message($_ticketReport, $msg, $markup, $id);
} elseif ($user['step'] == 'transferID') {
    //>> بررسی عدد بودن
    if (!(is_numeric($text)) || $text == $user_id) {
        $msg = "❌ مقدار ورودی صحیح نیست.
لطفا شماره کاربری مقصد را با استفاده از اعداد انگلیسی برای ربات ارسال کنید.";
        message($user_id, $msg);
        die;
    }
    //>> بررسی وجود کاربر
    $num = mysqli_num_rows(mysqli_query($db, "SELECT * FROM user WHERE uid='$text'"));
    if ($num == 0) {
        $msg = "❌ کاربری با این یوزر آی دی در دیتابیس ثبت نشده است.

لطفا مجددا تلاش کنید:";
        message($user_id, $msg, $backMarkup);
        die;
    }
    mysqli_query($db, "UPDATE user SET  datam='$text' WHERE uid='$user_id'");
    //>> پیام به کاربر
    $wallet = $user['wallet'];
    $msg = "💸 با توجه به موجودیتان، شما میتوانید حداکثر مبلغ $wallet تومان را به حساب کاربران دیگر منتقل کنید
لطفا مبلغی که میخواهید به کاربر مورد نظر انتقال دهید را بر حسب تومان وارد کنید:";
    message($user_id, $msg, $backMarkup);
    setStep($user_id, 'transferCost');
    die;
} elseif ($user['step'] == 'transferCost') {
    //>> بررسی عدد بودن
    if (!(is_numeric($text)) or $text <= 0) {
        $msg = "❌ مقدار ورودی صحیح نیست.
لطفا مبلغ مورد نظر را با استفاده از اعداد انگلیسی برای ربات ارسال کنید.";
        message($user_id, $msg);
        die;
    }
    //>> بررسی موجودی
    $wallet = $user['wallet'];
    if ($text > $wallet) {
        $msg = "❌ مقدار وارد شده از موجودی شما بیشتر است. شما میتوانید حداکثر مبلغ $wallet را انتقال دهید.

لطفا مجددا مبلغ مورد نظر جهت انتقال را وارد کنید:";
        message($user_id, $msg);
        die;
    }

    $goal = $user['datam'];
    //>> پیام به کاربر
    $msg = "✅ #انتقال خروجی با موفقیت انجام شد!

مبلغ $text تومان از حساب شما به حساب کاربری $goal منتقل گردید.";
    message($user_id, $msg, $mianMarkup);
    setStep($user_id, 'none');
    //>> پیام به مقصد
    $msg = "✅ #انتقال ورودی با موفقیت انجام شد!

مبلغ $text تومان از حساب کاربری $user_id به حساب کاربری شما منتقل گردید.";
    message($goal, $msg);
    //>> گزارش به کانال پرداختی
    $msg = "🏧 #انتقال

🛫 مبدا :  <a href='tg://user?id=" . $user_id . "'>" . $user_id . "</a>
🛬 مقصد :  <a href='tg://user?id=" . $goal . "'>" . $goal . "</a>
💵 $text تومان";
    message($_payReport, $msg);
    //>> تغییرات دیتابیس
    mysqli_query($db, "UPDATE user SET wallet=wallet-$text WHERE uid='$user_id'");
    mysqli_query($db, "UPDATE user SET wallet=wallet+$text WHERE uid='$goal'");
    die;
} elseif ($user['step'] == 'answer') {
    $code = $user['datash'];
    if ($photo !== null) {
        photo($code, $photo, $caption);
    } elseif ($voice !== null) {
        voice($code, $voice, $caption);
    } else {
        message($code, $text);
    }
    $msg = "✅ پیام شما با موفقیت ارسال شد.";
    message($user_id, $msg, $mainMarkup);
    setStep($user_id, 'home');
} elseif ($user['step'] == 'phoneSend') {
    //>> بررسی ورودی
    if ($phone == null) {
        $msg = "لطفا از دکمه 
» $phoneErsal »
برای ارسال شماره استفاده کنید.";
        message($user_id, $msg, $phoneMarkup);
        die();
    }

    //>> بررسی شماره
    if (strpos("1" . $phone, '989') !== 1 and strpos("1" . $phone, '989') !== 2) {
        $msg = "امکان ثبت نام با شماره خارج کشور وجود ندارد!";
        message($chat_id, $msg, $phoneMarkup);
        die();
    }

    if ($contactId != $user_id) {
        $msg = urlencode("❌ ثبت شماره موبایل دیگران امکان پذیر نمی باشد.");
        $markup = $mainMarkup;
        $markup = json_encode($markup);
        message($chat_id, $msg, $markup);
        setStep($user_id, 'home');
        die();
    }

    //>> ارسال پیامک
    //<<----------- KAVEH API ----------->>\\
    $lastsms = $user['lastSMS'] + 60;
    if (time() < $lastsms) {
        $lastsms = $lastsms - time();
        $msg = "
$lastsms ثانیه دیگر تلاش کنید

";
        message($user_id, $msg);
        die();
    }
    $rand = rand(11111, 99999);
    $des = urlencode($_botName);
    // message(452323199, "https://arctrobots.ir/arcsmsapi/server.php?token=$_smsToken&phone=$phone&code=$rand&bot=$des");
    // $smsRes = file_get_contents("https://arctrobots.ir/arcsmsapi/server.php?token=$_smsToken&phone=$phone&code=$rand&bot=$des");

    $response = $smsRes;


    //>> بررسی ارسال
    if (false && $response == null) {
        $msg = "ارسال کد ناموفق بود!
لطفا دوباره تلاش کنید:";
        message($user_id, $msg, $phoneMarkup);
        setStep($user_id, "phoneSend");
        file_put_contents("smserr.txt", $response . PHP_EOL . PHP_EOL, FILE_APPEND);
        die;
    } else {
        $time = time();
        mysqli_query($db, "UPDATE user SET phone='$phone' , datam='$rand' , lastSMS='$time' WHERE uid='$user_id'");
        //>> پیام به کاربر
        $msg = "
✅ شماره شما تایید شد.

یکی از گزینه های زیر را انتخاب کنید :";
        message($user_id, $msg, $mainMarkup);
        setStep($user_id, 'home');
        //>> تغییر دیتابیس
        mysqli_query($db, "UPDATE user SET status='1' WHERE uid='$user_id'");
    }
} elseif ($user['step'] == 'phoneVerify') {
    //>> بررسی ورودی
    $code = $user['datam'];
    if ($text !== $code) {
        $msg = "
❌ کد وارد شده صحیح نمی باشد.
";
        message($user_id, $msg, $mainMarkup);
        setStep($user_id, 'home');
        die();
    }
    //>> پیام به کاربر
    $msg = "
✅ شماره شما تایید شد.

یکی از گزینه های زیر را انتخاب کنید :";
    message($user_id, $msg, $mainMarkup);
    setStep($user_id, 'home');
    //>> تغییر دیتابیس
    mysqli_query($db, "UPDATE user SET status='1' WHERE uid='$user_id'");
}

//<<----------------- END ----------------->>\\
elseif ($message) {
    message($user_id, $_startMsg, $mainMarkup);
    setStep($user_id, "none");
    die;
}
//unlink("error_log");
?>