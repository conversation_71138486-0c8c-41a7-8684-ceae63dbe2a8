<?php
/**
 * NOWPayments Configuration
 * تنظیمات NOWPayments
 */

// NOWPayments API Configuration
define('NOWPAYMENTS_API_KEY', 'YOUR_NOWPAYMENTS_API_KEY'); // کلید API شما
define('NOWPAYMENTS_SANDBOX', true); // true برای sandbox، false برای production

// URLs
if (NOWPAYMENTS_SANDBOX) {
    define('NOWPAYMENTS_API_URL', 'https://api-sandbox.nowpayments.io/v1/');
} else {
    define('NOWPAYMENTS_API_URL', 'https://api.nowpayments.io/v1/');
}

// Supported currencies for crypto payments
$supportedCryptos = [
    'trx' => 'TRON (TRX)',
    'usdttrc20' => 'USDT (TRC20)',
    'doge' => 'Dogecoin (DOGE)'
];

/**
 * NOWPayments API Helper Functions
 */

/**
 * Make API request to NOWPayments
 */
function nowpayments_api_request($endpoint, $method = 'GET', $data = null) {
    $url = NOWPAYMENTS_API_URL . $endpoint;
    
    $headers = [
        'x-api-key: ' . NOWPAYMENTS_API_KEY,
        'Content-Type: application/json'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($response === false) {
        return ['error' => 'cURL Error'];
    }
    
    $decoded = json_decode($response, true);
    
    if ($httpCode !== 200) {
        return ['error' => $decoded['message'] ?? 'API Error', 'code' => $httpCode];
    }
    
    return $decoded;
}

/**
 * Get available currencies
 */
function nowpayments_get_currencies() {
    return nowpayments_api_request('currencies');
}

/**
 * Get estimate for payment
 */
function nowpayments_get_estimate($amount, $currency_from, $currency_to) {
    $endpoint = "estimate?amount={$amount}&currency_from={$currency_from}&currency_to={$currency_to}";
    return nowpayments_api_request($endpoint);
}

/**
 * Create payment
 */
function nowpayments_create_payment($price_amount, $price_currency, $pay_currency, $order_id, $order_description = '') {
    $data = [
        'price_amount' => $price_amount,
        'price_currency' => $price_currency,
        'pay_currency' => $pay_currency,
        'order_id' => $order_id,
        'order_description' => $order_description
    ];
    
    return nowpayments_api_request('payment', 'POST', $data);
}

/**
 * Get payment status
 */
function nowpayments_get_payment_status($payment_id) {
    return nowpayments_api_request("payment/{$payment_id}");
}

/**
 * Convert USD to Toman
 */
function usd_to_toman($usd_amount) {
    $dollar_price = file_get_contents('admin/dollarprice.php');
    $dollar_price = (int) trim($dollar_price);
    return $usd_amount * $dollar_price;
}

/**
 * Convert Toman to USD
 */
function toman_to_usd($toman_amount) {
    $dollar_price = file_get_contents('admin/dollarprice.php');
    $dollar_price = (int) trim($dollar_price);
    return round($toman_amount / $dollar_price, 2);
}
?>
