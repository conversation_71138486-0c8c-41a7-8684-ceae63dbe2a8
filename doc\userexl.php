<?php  
                    
require "../function.php";
require "jdf.php";

//>> گرفتن پارامترها
$token=$_GET['token'];

$start=$_GET['start'];
$end=$_GET['end'];

$names=$_GET['name'];

if($start==0 or $start==null) $start=1;
if($end==0 or $end==null) $end=0;

$start=time()-$start*30*24*3600;
$end=time()-$end*30*24*3600;

test("$start to $end");

if($token!="4SAF4dsaf4DSAF584asdfasd") die;


$setSql = "SELECT * FROM `user` WHERE time>$start AND time<$end";
test($setSql);
$setRec = mysqli_query($db, $setSql);  

$columnHeader = '';

// $colQuery=mysqli_query($db,"SELECT `COLUMN_NAME` FROM `INFORMATION_SCHEMA`.`COLUMNS` WHERE `TABLE_SCHEMA` = 'anghaair_testDataBase' AND `TABLE_NAME` = 'orders'");
// while($col=mysqli_fetch_assoc($colQuery)['COLUMN_NAME']){
//     $columnHeader=$columnHeader."$col"."\t";
// }
$columnHeader = "ID Number" . "\t" . "Name" . "\t" . "UserName" . "\t" . "Mobile Number" . "\t" . "Count Orders Numbers" . "\t" . "Start Date" . "\t" . "Start Time" . "\t";  
    
$setData = '';  
  
while ($rec = mysqli_fetch_assoc($setRec)) {  
    $rowData = '';  
    
    $uid=$rec['uid'];
    $name=$rec['name'];
    $phone=$rec['phone'];
    $username=$rec['username'];
    $orders=mysqli_num_rows(mysqli_query($db,"SELECT * FROM orders WHERE uid='$uid'"));
    $time=$rec['time'];
    $date=jdate("Y/m/d",$time);
    $hour=jdate("H:i",$time);
    
    
    $value = '"' . $uid . '"' . "\t";  
    $rowData .= $value;
    
    $value = '"' . $name . '"' . "\t";  
    $rowData .= $value;
    
    $value = '"' . $username . '"' . "\t";  
    $rowData .= $value;
    
    $value = '"' . $phone . '"' . "\t";  
    $rowData .= $value;
    
    $value = '"' . $orders . '"' . "\t";  
    $rowData .= $value;
    
    $value = '"' . $date . '"' . "\t";  
    $rowData .= $value;
    
    $value = '"' . $hour . '"' . "\t";  
    $rowData .= $value;
    
    $setData .= trim($rowData) . "\n";  
}  
  
 
header("Content-type: application/octet-stream");  
header("Content-Disposition: attachment; filename=$names.xls");  
header('Content-Transfer-Encoding: binary');
header("Pragma: no-cache");  
header("Expires: 0");  

echo chr(255).chr(254).iconv("UTF-8", "UTF-16LE//IGNORE", $columnHeader . "\n" . $setData . "\n");

exit()

?>