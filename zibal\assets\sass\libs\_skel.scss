// skel.scss v3.0.1 | (c) skel.io | MIT licensed */

// Vars.

	/// Breakpoints.
	/// @var {list}
	$breakpoints: () !global;

	/// Vendor prefixes.
	/// @var {list}
	$vendor-prefixes: (
		'-moz-',
		'-webkit-',
		'-ms-',
		''
	);

	/// Properties that should be vendorized.
	/// @var {list}
	$vendor-properties: (
		'align-content',
		'align-items',
		'align-self',
		'animation',
		'animation-delay',
		'animation-direction',
		'animation-duration',
		'animation-fill-mode',
		'animation-iteration-count',
		'animation-name',
		'animation-play-state',
		'animation-timing-function',
		'appearance',
		'backface-visibility',
		'box-sizing',
		'filter',
		'flex',
		'flex-basis',
		'flex-direction',
		'flex-flow',
		'flex-grow',
		'flex-shrink',
		'flex-wrap',
		'justify-content',
		'order',
		'perspective',
		'pointer-events',
		'transform',
		'transform-origin',
		'transform-style',
		'transition',
		'transition-delay',
		'transition-duration',
		'transition-property',
		'transition-timing-function',
		'user-select'
	);

	/// Values that should be vendorized.
	/// @var {list}
	$vendor-values: (
		'filter',
		'flex',
		'linear-gradient',
		'radial-gradient',
		'transform'
	);

// Functions.

	/// Removes a specific item from a list.
	/// <AUTHOR> Giraudel
	/// @param {list} $list List.
	/// @param {integer} $index Index.
	/// @return {list} Updated list.
	@function remove-nth($list, $index) {

		$result: null;

		@if type-of($index) != number {
			@warn "$index: #{quote($index)} is not a number for `remove-nth`.";
		}
		@else if $index == 0 {
			@warn "List index 0 must be a non-zero integer for `remove-nth`.";
		}
		@else if abs($index) > length($list) {
			@warn "List index is #{$index} but list is only #{length($list)} item long for `remove-nth`.";
		}
		@else {

			$result: ();
			$index: if($index < 0, length($list) + $index + 1, $index);

			@for $i from 1 through length($list) {

				@if $i != $index {
					$result: append($result, nth($list, $i));
				}

			}

		}

		@return $result;

	}

	/// Replaces a substring within another string.
	/// <AUTHOR> Giraudel
	/// @param {string} $string String.
	/// @param {string} $search Substring.
	/// @param {string} $replace Replacement.
	/// @return {string} Updated string.
	@function str-replace($string, $search, $replace: '') {

		$index: str-index($string, $search);

		@if $index {
			@return str-slice($string, 1, $index - 1) + $replace + str-replace(str-slice($string, $index + str-length($search)), $search, $replace);
		}

		@return $string;

	}

	/// Replaces a substring within each string in a list.
	/// @param {list} $strings List of strings.
	/// @param {string} $search Substring.
	/// @param {string} $replace Replacement.
	/// @return {list} Updated list of strings.
	@function str-replace-all($strings, $search, $replace: '') {

		@each $string in $strings {
			$strings: set-nth($strings, index($strings, $string), str-replace($string, $search, $replace));
		}

		@return $strings;

	}

	/// Gets a value from a map.
	/// <AUTHOR> Giraudel
	/// @param {map} $map Map.
	/// @param {string} $keys Key(s).
	/// @return {string} Value.
	@function val($map, $keys...) {

		@if nth($keys, 1) == null {
			$keys: remove-nth($keys, 1);
		}

		@each $key in $keys {
			$map: map-get($map, $key);
		}

		@return $map;

	}

// Mixins.

	/// Sets the global box model.
	/// @param {string} $model Model (default is content).
	@mixin boxModel($model: 'content') {

		$x: $model + '-box';

		*, *:before, *:after {
			-moz-box-sizing: #{$x};
			-webkit-box-sizing: #{$x};
			box-sizing: #{$x};
		}

	}

	/// Wraps @content in a @media block using a given breakpoint.
	/// @param {string} $breakpoint Breakpoint.
	/// @param {map} $queries Additional queries.
	@mixin breakpoint($breakpoint: null, $queries: null) {

		$query: 'screen';

		// Breakpoint.
			@if $breakpoint and map-has-key($breakpoints, $breakpoint) {
				$query: $query + ' and ' + map-get($breakpoints, $breakpoint);
			}

		// Queries.
			@if $queries {
				@each $k, $v in $queries {
					$query: $query + ' and (' + $k + ':' + $v + ')';
				}
			}

        @media #{$query} {
            @content;
        }

	}

	/// Wraps @content in a @media block targeting a specific orientation.
	/// @param {string} $orientation Orientation.
	@mixin orientation($orientation) {
		@media screen and (orientation: #{$orientation}) {
			@content;
		}
	}

	/// Utility mixin for containers.
	/// @param {mixed} $width Width.
	@mixin containers($width) {

		// Locked?
			$lock: false;

			@if length($width) == 2 {
				$width: nth($width, 1);
				$lock: true;
			}

		// Modifiers.
			.container.\31 25\25		{ width: 100%; max-width: $width * 1.25; min-width: $width; }
			.container.\37 5\25			{ width: $width * 0.75; }
			.container.\35 0\25			{ width: $width * 0.5; }
			.container.\32 5\25			{ width: $width * 0.25; }

		// Main class.
			.container {
				@if $lock {
					width: $width !important;
				}
				@else {
					width: $width;
				}
			}

	}

	/// Utility mixin for grid.
	/// @param {list} $gutters Column and row gutters (default is 40px).
	/// @param {string} $breakpointName Optional breakpoint name.
	@mixin grid($gutters: 40px, $breakpointName: null) {

		// Gutters.
			@include grid-gutters($gutters);
			@include grid-gutters($gutters, \32 00\25, 2);
			@include grid-gutters($gutters, \31 50\25, 1.5);
			@include grid-gutters($gutters, \35 0\25, 0.5);
			@include grid-gutters($gutters, \32 5\25, 0.25);

		// Cells.
			$x: '';

			@if $breakpointName {
				$x: '\\28' + $breakpointName + '\\29';
			}

			.\31 2u#{$x}, .\31 2u\24#{$x} { width: 100%; clear: none; margin-left: 0; }
			.\31 1u#{$x}, .\31 1u\24#{$x} { width: 91.6666666667%; clear: none; margin-left: 0; }
			.\31 0u#{$x}, .\31 0u\24#{$x} { width: 83.3333333333%; clear: none; margin-left: 0; }
			.\39 u#{$x}, .\39 u\24#{$x} { width: 75%; clear: none; margin-left: 0; }
			.\38 u#{$x}, .\38 u\24#{$x} { width: 66.6666666667%; clear: none; margin-left: 0; }
			.\37 u#{$x}, .\37 u\24#{$x} { width: 58.3333333333%; clear: none; margin-left: 0; }
			.\36 u#{$x}, .\36 u\24#{$x} { width: 50%; clear: none; margin-left: 0; }
			.\35 u#{$x}, .\35 u\24#{$x} { width: 41.6666666667%; clear: none; margin-left: 0; }
			.\34 u#{$x}, .\34 u\24#{$x} { width: 33.3333333333%; clear: none; margin-left: 0; }
			.\33 u#{$x}, .\33 u\24#{$x} { width: 25%; clear: none; margin-left: 0; }
			.\32 u#{$x}, .\32 u\24#{$x} { width: 16.6666666667%; clear: none; margin-left: 0; }
			.\31 u#{$x}, .\31 u\24#{$x} { width: 8.3333333333%; clear: none; margin-left: 0; }

			.\31 2u\24#{$x} + *,
			.\31 1u\24#{$x} + *,
			.\31 0u\24#{$x} + *,
			.\39 u\24#{$x} + *,
			.\38 u\24#{$x} + *,
			.\37 u\24#{$x} + *,
			.\36 u\24#{$x} + *,
			.\35 u\24#{$x} + *,
			.\34 u\24#{$x} + *,
			.\33 u\24#{$x} + *,
			.\32 u\24#{$x} + *,
			.\31 u\24#{$x} + * {
				clear: left;
			}

			.\-11u#{$x} { margin-left: 91.6666666667% }
			.\-10u#{$x} { margin-left: 83.3333333333% }
			.\-9u#{$x} { margin-left: 75% }
			.\-8u#{$x} { margin-left: 66.6666666667% }
			.\-7u#{$x} { margin-left: 58.3333333333% }
			.\-6u#{$x} { margin-left: 50% }
			.\-5u#{$x} { margin-left: 41.6666666667% }
			.\-4u#{$x} { margin-left: 33.3333333333% }
			.\-3u#{$x} { margin-left: 25% }
			.\-2u#{$x} { margin-left: 16.6666666667% }
			.\-1u#{$x} { margin-left: 8.3333333333% }

	}

	/// Utility mixin for grid.
	/// @param {list} $gutters Gutters.
	/// @param {string} $class Optional class name.
	/// @param {integer} $multiplier Multiplier (default is 1).
	@mixin grid-gutters($gutters, $class: null, $multiplier: 1) {

		// Expand gutters if it's not a list.
			@if length($gutters) == 1 {
				$gutters: ($gutters, 0);
			}

		// Get column and row gutter values.
			$c: nth($gutters, 1);
			$r: nth($gutters, 2);

		// Get class (if provided).
			$x: '';

			@if $class {
				$x: '.' + $class;
			}

		// Default.
			.row#{$x} > * { padding: ($r * $multiplier) 0 0 ($c * $multiplier); }
			.row#{$x} { margin: ($r * $multiplier * -1) 0 -1px ($c * $multiplier * -1); }

		// Uniform.
			.row.uniform#{$x} > * { padding: ($c * $multiplier) 0 0 ($c * $multiplier); }
			.row.uniform#{$x} { margin: ($c * $multiplier * -1) 0 -1px ($c * $multiplier * -1); }

	}

	/// Wraps @content in vendorized keyframe blocks.
	/// @param {string} $name Name.
	@mixin keyframes($name) {

		@-moz-keyframes #{$name} { @content; }
		@-webkit-keyframes #{$name} { @content; }
		@-ms-keyframes #{$name} { @content; }
		@keyframes #{$name} { @content; }

	}

	///
	/// Sets breakpoints.
	/// @param {map} $x Breakpoints.
	///
	@mixin skel-breakpoints($x: ()) {
		$breakpoints: $x !global;
	}

	///
	/// Initializes layout module.
	/// @param {map} config Config.
	///
	@mixin skel-layout($config: ()) {

		// Config.
			$configPerBreakpoint: ();

			$z: map-get($config, 'breakpoints');

			@if $z {
				$configPerBreakpoint: $z;
			}

		// Reset.
			$x: map-get($config, 'reset');

			@if $x {

				/* Reset */

				@include reset($x);

			}

		// Box model.
			$x: map-get($config, 'boxModel');

			@if $x {

				/* Box Model */

				@include boxModel($x);

			}

		// Containers.
			$containers: map-get($config, 'containers');

			@if $containers {

				/* Containers */

				.container {
					margin-left: auto;
					margin-right: auto;
				}

				// Use default is $containers is just "true".
					@if $containers == true {
						$containers: 960px;
					}

				// Apply base.
					@include containers($containers);

				// Apply per-breakpoint.
					@each $name in map-keys($breakpoints) {

						// Get/use breakpoint setting if it exists.
							$x: map-get($configPerBreakpoint, $name);

							// Per-breakpoint config exists?
								@if $x {
									$y: map-get($x, 'containers');

									// Setting exists? Use it.
										@if $y {
											$containers: $y;
										}

								}

						// Create @media block.
							@media screen and #{map-get($breakpoints, $name)} {
								@include containers($containers);
							}

					}

			}

		// Grid.
			$grid: map-get($config, 'grid');

			@if $grid {

				/* Grid */

				// Use defaults if $grid is just "true".
					@if $grid == true {
						$grid: ();
					}

				// Sub-setting: Gutters.
					$grid-gutters: 40px;
					$x: map-get($grid, 'gutters');

					@if $x {
						$grid-gutters: $x;
					}

				// Rows.
					.row {
						border-bottom: solid 1px transparent;
						-moz-box-sizing: border-box;
						-webkit-box-sizing: border-box;
						box-sizing: border-box;
					}

					.row > * {
						float: left;
						-moz-box-sizing: border-box;
						-webkit-box-sizing: border-box;
						box-sizing: border-box;
					}

					.row:after, .row:before {
						content: '';
						display: block;
						clear: both;
						height: 0;
					}

					.row.uniform > * > :first-child {
						margin-top: 0;
					}

					.row.uniform > * > :last-child {
						margin-bottom: 0;
					}

				// Gutters (0%).
					@include grid-gutters($grid-gutters, \30 \25, 0);

				// Apply base.
					@include grid($grid-gutters);

				// Apply per-breakpoint.
					@each $name in map-keys($breakpoints) {

						// Get/use breakpoint setting if it exists.
							$x: map-get($configPerBreakpoint, $name);

							// Per-breakpoint config exists?
								@if $x {
									$y: map-get($x, 'grid');

									// Setting exists?
										@if $y {

											// Sub-setting: Gutters.
												$x: map-get($y, 'gutters');

												@if $x {
													$grid-gutters: $x;
												}

										}

								}

						// Create @media block.
							@media screen and #{map-get($breakpoints, $name)} {
								@include grid($grid-gutters, $name);
							}

					}

			}

	}

	/// Resets browser styles.
	/// @param {string} $mode Mode (default is 'normalize').
	@mixin reset($mode: 'normalize') {

		@if $mode == 'normalize' {

			// normalize.css v3.0.2 | MIT License | git.io/normalize
				html{font-family:sans-serif;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}body{margin:0}article,aside,details,figcaption,figure,footer,header,hgroup,main,menu,nav,section,summary{display:block}audio,canvas,progress,video{display:inline-block;vertical-align:baseline}audio:not([controls]){display:none;height:0}[hidden],template{display:none}a{background-color:transparent}a:active,a:hover{outline:0}abbr[title]{border-bottom:1px dotted}b,strong{font-weight:700}dfn{font-style:italic}h1{font-size:2em;margin:.67em 0}mark{background:#ff0;color:#000}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sup{top:-.5em}sub{bottom:-.25em}img{border:0}svg:not(:root){overflow:hidden}figure{margin:1em 40px}hr{-moz-box-sizing:content-box;box-sizing:content-box;height:0}pre{overflow:auto}code,kbd,pre,samp{font-family:monospace,monospace;font-size:1em}button,input,optgroup,select,textarea{color:inherit;font:inherit;margin:0}button{overflow:visible}button,select{text-transform:none}button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}button[disabled],html input[disabled]{cursor:default}button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}input{line-height:normal}input[type=checkbox],input[type=radio]{box-sizing:border-box;padding:0}input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button{height:auto}input[type=search]{-webkit-appearance:textfield;-moz-box-sizing:content-box;-webkit-box-sizing:content-box;box-sizing:content-box}input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration{-webkit-appearance:none}fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}legend{border:0;padding:0}textarea{overflow:auto}optgroup{font-weight:700}table{border-collapse:collapse;border-spacing:0}td,th{padding:0}

		}
		@else if $mode == 'full' {

			// meyerweb.com/eric/tools/css/reset v2.0 | 20110126 | License: none (public domain)
				html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block;}body{line-height:1;}ol,ul{list-style:none;}blockquote,q{quotes:none;}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}table{border-collapse:collapse;border-spacing:0;}body{-webkit-text-size-adjust:none}

		}

	}

	/// Vendorizes a declaration's property and/or value(s).
	/// @param {string} $property Property.
	/// @param {mixed} $value String/list of value(s).
	@mixin vendor($property, $value) {

		// Determine if property should expand.
			$expandProperty: index($vendor-properties, $property);

		// Determine if value should expand (and if so, add '-prefix-' placeholder).
			$expandValue: false;

			@each $x in $value {
				@each $y in $vendor-values {
					@if $y == str-slice($x, 1, str-length($y)) {

						$value: set-nth($value, index($value, $x), '-prefix-' + $x);
						$expandValue: true;

					}
				}
			}

		// Expand property?
			@if $expandProperty {
			    @each $vendor in $vendor-prefixes {
			        #{$vendor}#{$property}: #{str-replace-all($value, '-prefix-', $vendor)};
			    }
			}

		// Expand just the value?
			@elseif $expandValue {
			    @each $vendor in $vendor-prefixes {
			        #{$property}: #{str-replace-all($value, '-prefix-', $vendor)};
			    }
			}

		// Neither? Treat them as a normal declaration.
			@else {
		        #{$property}: #{$value};
			}

	}