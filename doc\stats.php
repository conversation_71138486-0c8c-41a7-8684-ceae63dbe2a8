<?php
unlink("error_log");

require "../function.php";

//>> بررسی سفارش
echo "<pre>";
$orders = mysqli_query($db, "SELECT * FROM orders WHERE api>0 AND status<2 ORDER BY `id` DESC LIMIT 10");
while ($order = mysqli_fetch_assoc($orders)) {
    echo $track . "<br>";
    $track = $order['track'];
    $uid = $order['uid'];
    $cost = $order['cost'];
    $quantity = $order["quantity"];
    $apiid = $order["api"];

    $api = mysqli_query($db, "SELECT * FROM apis WHERE id = '$apiid'");
    if (mysqli_num_rows($api) < 1)
        continue;

    $api = mysqli_fetch_assoc($api);

    $token = $api['token']; 
    $site = $api['site'];

    //>> دریافت دیتا
    $data = array(
        'key' => $token,
        // (دریافتی ( برای همه عملیات Api      
        'action' => 'status',
        // (عملیات (فقط برای ثبت سفارش اختیاری برای سایر عملیات اجباری
        'order' => "$track",
    );
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $site);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    $stats = json_decode($result, true);
    curl_close($ch);
    //>> بررسی وضعیت
    $status = $stats["status"];
    $remains = $stats['remains'];
    $start_count = $stats['start_count'];
    
    echo "$status";


    //>> تغییر دیتابیس
    mysqli_query($db, "UPDATE orders SET remains='$remains' , start_count='$start_count' WHERE track='$track'");
    if ($status == 'In progress') {
        mysqli_query($db, "UPDATE orders SET status='1' WHERE track='$track'");
    } elseif ($status == 'Completed') {
        mysqli_query($db, "UPDATE orders SET status='2' WHERE track='$track'");
        //>> اطلاع به کاربر
        $msg = "✅ سفارش شما با کد پیگیری $track با موفقیت انجام شد.";
        message($uid, $msg);
    } elseif ($status == 'Canceled') {
        mysqli_query($db, "UPDATE orders SET status='3' WHERE track='$track'");

        //>> بازگشت مبلغ
        mysqli_query($db, "UPDATE user SET wallet=wallet+$cost WHERE uid='$uid'");

        //>> اطلاع به کاربر
        $msg = "💢 متاسفانه سفارش شما با کد پیگیری $track لغو شد و مبلغ $cost تومان به حساب شما بازگشت داده شد.";
        message($uid, $msg);
        
        $_payReport = file_get_contents("../doc/adminPaymentChannel.txt");

        $msg = "#برگشت هزینه بابت لغو سفارش $track به مبلغ $cost تومان برای کاربر <a href='tg://user?id=".$user_id."'>".$user_id."</a> انجام شد. ";
        message($_payReport,$msg);

    } elseif ($status == 'Partial') {
        mysqli_query($db, "UPDATE orders SET status='4' WHERE track='$track'");
        $remains = $remains + 0;
        $cost = $cost * ($remains / $quantity);

        //>> بازگشت مبلغ
        mysqli_query($db, "UPDATE user SET wallet=wallet+$cost WHERE uid='$uid'");

        //>> اطلاع به کاربر
        $msg = "💢 متاسفانه سفارش شما با کد پیگیری $track ناتمام ماند و مبلغ $cost تومان به حساب شما بازگشت داده شد.";
        message($uid, $msg);
        
        $_payReport = file_get_contents("../doc/adminPaymentChannel.txt");
        $msg = "#برگشت هزینه بابت نیمه تمام ماندن سفارش $track به مبلغ $cost تومان برای کاربر <a href='tg://user?id=".$user_id."'>".$user_id."</a> انجام شد. ";
        message($_payReport,$msg);

    } // هنوز درحال انجام بود


} // وبسرویس نبود



// //>> بررسی قیمت ها
// $data = array(
//     'key' => TOKEN_SITE,
//     // (دریافتی از آذر سوشال (اجباری برای همه عملیات Api      
//     'action' => 'services', // (عملیات (فقط برای ثبت سفارش اختیاری برای سایر عملیات اجباری
// );
// $ch = curl_init();
// curl_setopt($ch, CURLOPT_URL, API_SITE);
// curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
// curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
// $result = curl_exec($ch);
// $services = json_decode($result, true);
// // file_put_contents('results.txt', date("Y/n/d G:i:s").PHP_EOL.$result.PHP_EOL.PHP_EOL,FILE_APPEND);
// curl_close($ch);

// //>> پارامتر های محصول
// $i = 0;

// $dbservices = mysqli_query($db, "SELECT * FROM product WHERE api>0");

// $dbapiproducts = [];

// while ($dbpro = mysqli_fetch_assoc($dbservices)) {
//     $dbapicode = $dbpro['api'];
//     $dbapilastprice = $dbpro['last_api_price'];
//     $dbapiproducts["$dbapicode"] = "$dbapilastprice";
// }


// while ($serviceParams = $services[$i]) {
//     $i++;
//     $service = $serviceParams['service'];

//     if (array_key_exists($service, $dbapiproducts)) {
//         $rate = $serviceParams['rate'];
//         $rate /= 1000;

//         if ($dbapiproducts["$service"] < $rate) {
//             $dif = $rate * 1000 - $dbapiproducts["$service"] * 1000;
//             $dif /= 1000;
//             mysqli_query($db, "UPDATE product SET last_api_price=$rate, price=price+$dif WHERE api='$service'");

//             $name = $serviceParams['name'];
//             $msg = "💢 ادمین محترم
// قیمت محصول
// « $name »
// در وبسرویس از " . $dbapiproducts["$service"] . " به $rate تغییر کرد.
// به صورت خودکار به قیمت محصول در ربات مقدار $dif اضافه گردید.
// ";

//             foreach ($admins as $admin) {
//                 message($admin, $msg);
//             }

//         }
//     }

// }

?>